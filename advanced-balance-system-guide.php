<?php
/**
 * دليل نظام الرصيد المتقدم
 * Advanced Balance System Guide
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل نظام الرصيد المتقدم - نظام Zero</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .concept-box {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        .example-box {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .scenario-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .creditor { color: #28a745; font-weight: bold; }
        .debtor { color: #dc3545; font-weight: bold; }
        .neutral { color: #6c757d; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-balance-scale me-3"></i>
                دليل نظام الرصيد المتقدم
            </h1>
            <p class="lead">فهم شامل لمفاهيم المدين والدائن ونظام الرصيد المحفوظ</p>
            <div class="badge bg-success fs-5 px-4 py-2">
                <i class="fas fa-graduation-cap me-2"></i>
                دليل تعليمي متقدم
            </div>
        </div>

        <!-- المفاهيم الأساسية -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-book me-2"></i>المفاهيم الأساسية</h3>
            </div>
            <div class="card-body">
                
                <div class="concept-box">
                    <h5><i class="fas fa-user-minus text-danger me-2"></i>المدين (Debtor)</h5>
                    <p><strong>التعريف:</strong> هو الشخص أو الكيان الذي <strong>عليه التزام مالي</strong> تجاه طرف آخر.</p>
                    <p><strong>الخصائص:</strong></p>
                    <ul>
                        <li>استلم قيمة (نقد، سلعة، خدمة) ولم يسددها بعد</li>
                        <li>عليه مسؤولية السداد في المستقبل</li>
                        <li>يظهر في النظام برصيد سالب أو دين مستحق</li>
                    </ul>
                    
                    <div class="example-box">
                        <h6>مثال:</h6>
                        <p>عميل اشترى سلعة بقيمة 100 ريال ولم يدفع → العميل <span class="debtor">مدين</span> للمحل بمبلغ 100 ريال</p>
                    </div>
                </div>
                
                <div class="concept-box">
                    <h5><i class="fas fa-user-plus text-success me-2"></i>الدائن (Creditor)</h5>
                    <p><strong>التعريف:</strong> هو الشخص أو الكيان الذي <strong>له حق مالي</strong> على طرف آخر.</p>
                    <p><strong>الخصائص:</strong></p>
                    <ul>
                        <li>قدم قيمة (نقد، سلعة، خدمة) ولم يستلم مقابلها بعد</li>
                        <li>له حق المطالبة بالسداد</li>
                        <li>يظهر في النظام برصيد موجب أو مبلغ مستحق له</li>
                    </ul>
                    
                    <div class="example-box">
                        <h6>مثال:</h6>
                        <p>المحل باع سلعة بقيمة 100 ريال ولم يستلم الثمن → المحل <span class="creditor">دائن</span> على العميل بمبلغ 100 ريال</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- نظام الرصيد المتقدم -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h3><i class="fas fa-wallet me-2"></i>نظام الرصيد المتقدم في Zero</h3>
            </div>
            <div class="card-body">
                
                <h5>مكونات النظام:</h5>
                
                <div class="concept-box">
                    <h6><i class="fas fa-piggy-bank text-success me-2"></i>الرصيد المحفوظ (Wallet Balance)</h6>
                    <p>هو المبلغ الذي يحتفظ به النظام للعميل نتيجة:</p>
                    <ul>
                        <li><strong>الفكة الزائدة:</strong> عندما يدفع العميل أكثر من قيمة الفاتورة</li>
                        <li><strong>المدفوعات المسبقة:</strong> عندما يدفع العميل مقدماً</li>
                        <li><strong>المرتجعات:</strong> عند إرجاع سلع وتحويل قيمتها لرصيد</li>
                    </ul>
                    
                    <div class="scenario-box">
                        <h6>سيناريو الفكة الزائدة:</h6>
                        <p><strong>المشكلة:</strong> قيمة الفاتورة = 10.5 ريال، العميل يدفع = 11 ريال</p>
                        <p><strong>الحل:</strong> الفرق (0.5 ريال) يُحفظ في رصيد العميل المحفوظ</p>
                        <p><strong>النتيجة:</strong> العميل أصبح <span class="creditor">دائن</span> بمبلغ 0.5 ريال</p>
                    </div>
                </div>
                
                <div class="concept-box">
                    <h6><i class="fas fa-exclamation-triangle text-danger me-2"></i>رصيد الديون (Debt Balance)</h6>
                    <p>هو المبلغ المستحق على العميل نتيجة:</p>
                    <ul>
                        <li><strong>الشراء الآجل:</strong> عندما يأخذ العميل سلع ولا يدفع</li>
                        <li><strong>الدفع الجزئي:</strong> عندما يدفع العميل أقل من قيمة الفاتورة</li>
                        <li><strong>الخدمات المؤجلة:</strong> خدمات تم تقديمها ولم تُسدد</li>
                    </ul>
                    
                    <div class="scenario-box">
                        <h6>سيناريو الشراء الآجل:</h6>
                        <p><strong>المشكلة:</strong> قيمة الفاتورة = 50 ريال، العميل لا يدفع شيئاً</p>
                        <p><strong>الحل:</strong> المبلغ (50 ريال) يُسجل كدين على العميل</p>
                        <p><strong>النتيجة:</strong> العميل أصبح <span class="debtor">مدين</span> بمبلغ 50 ريال</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- السيناريوهات المتقدمة -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-scenarios me-2"></i>السيناريوهات المتقدمة</h3>
            </div>
            <div class="card-body">
                
                <div class="scenario-box">
                    <h5>السيناريو الأول: تراكم الرصيد المحفوظ</h5>
                    <p><strong>الحالة:</strong> عميل لديه رصيد محفوظ = 15 ريال</p>
                    <p><strong>الشراء الجديد:</strong> فاتورة بقيمة 12 ريال</p>
                    <p><strong>خيارات النظام:</strong></p>
                    <ul>
                        <li>عرض خيار: "هل تريد استخدام رصيدك المحفوظ؟"</li>
                        <li>إذا وافق: خصم 12 ريال من الرصيد، يتبقى 3 ريال</li>
                        <li>إذا رفض: يدفع نقداً ويحتفظ برصيده</li>
                    </ul>
                </div>
                
                <div class="scenario-box">
                    <h5>السيناريو الثاني: الخصم التلقائي من الديون</h5>
                    <p><strong>الحالة:</strong> عميل لديه رصيد محفوظ = 8 ريال، وعليه دين = 20 ريال</p>
                    <p><strong>الشراء الجديد:</strong> فاتورة بقيمة 15 ريال، لا يدفع شيئاً</p>
                    <p><strong>معالجة النظام:</strong></p>
                    <ol>
                        <li>خصم 8 ريال من الرصيد المحفوظ تلقائياً</li>
                        <li>إضافة 7 ريال (15-8) للديون</li>
                        <li>النتيجة: رصيد محفوظ = 0، ديون = 27 ريال</li>
                    </ol>
                </div>
                
                <div class="scenario-box">
                    <h5>السيناريو الثالث: الدفع المختلط</h5>
                    <p><strong>الحالة:</strong> عميل لديه رصيد محفوظ = 10 ريال</p>
                    <p><strong>الشراء الجديد:</strong> فاتورة بقيمة 25 ريال</p>
                    <p><strong>الدفع:</strong> 20 ريال نقداً + استخدام الرصيد المحفوظ</p>
                    <p><strong>النتيجة:</strong></p>
                    <ul>
                        <li>استخدام 10 ريال من الرصيد المحفوظ</li>
                        <li>دفع 15 ريال نقداً (25-10)</li>
                        <li>فكة زائدة = 5 ريال (20-15) تُضاف للرصيد المحفوظ</li>
                        <li>الرصيد النهائي = 5 ريال</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- الميزات التقنية -->
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-cogs me-2"></i>الميزات التقنية</h3>
            </div>
            <div class="card-body">
                
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-database text-primary me-2"></i>قاعدة البيانات:</h6>
                        <ul>
                            <li>جدول <code>balance_transactions</code> لتتبع جميع المعاملات</li>
                            <li>أعمدة <code>wallet_balance</code> و <code>debt_balance</code> منفصلة</li>
                            <li>إجراءات مخزنة للعمليات المعقدة</li>
                            <li>فيوز لعرض ملخصات الأرصدة</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-shield-alt text-success me-2"></i>الأمان والدقة:</h6>
                        <ul>
                            <li>معاملات قاعدة البيانات (Transactions)</li>
                            <li>تسجيل تفصيلي لجميع العمليات</li>
                            <li>التحقق من صحة البيانات</li>
                            <li>منع التلاعب في الأرصدة</li>
                        </ul>
                    </div>
                </div>
                
                <h6><i class="fas fa-calculator text-info me-2"></i>المحاسبة الدقيقة:</h6>
                <ul>
                    <li><strong>الرصيد المحفوظ:</strong> لا يدخل الخزينة حتى يُستخدم</li>
                    <li><strong>الديون:</strong> لا تُحسب في الخزينة، لكن تُتتبع بدقة</li>
                    <li><strong>الفكة:</strong> تُخصم من الخزينة وتُضاف للرصيد المحفوظ</li>
                    <li><strong>التقارير:</strong> تفريق واضح بين الأموال الفعلية والالتزامات</li>
                </ul>
            </div>
        </div>

        <!-- دليل الاستخدام -->
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h3><i class="fas fa-user-guide me-2"></i>دليل الاستخدام</h3>
            </div>
            <div class="card-body">
                
                <h6>للبائع:</h6>
                <ol>
                    <li>استخدم صفحة "المبيعات المتقدمة" للاستفادة من جميع الميزات</li>
                    <li>راقب أرصدة العملاء في صفحة "إدارة أرصدة العملاء"</li>
                    <li>استخدم التقارير لمتابعة الديون والأرصدة المحفوظة</li>
                    <li>قم بتحصيل الديون المتأخرة بانتظام</li>
                </ol>
                
                <h6>للعميل:</h6>
                <ul>
                    <li>يمكن الاستفادة من الرصيد المحفوظ في المشتريات التالية</li>
                    <li>لا حاجة لحمل فكة صغيرة - النظام يحفظها</li>
                    <li>إمكانية الشراء الآجل حسب حد الائتمان المحدد</li>
                    <li>تتبع واضح لجميع المعاملات والأرصدة</li>
                </ul>
            </div>
        </div>

        <!-- الروابط السريعة -->
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h3><i class="fas fa-link me-2"></i>الروابط السريعة</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <a href="apply-advanced-balance-update.php" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-database me-2"></i>
                            تطبيق التحديثات
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="pages/sales/add-with-advanced-balance.php" class="btn btn-success w-100 mb-2">
                            <i class="fas fa-cash-register me-2"></i>
                            مبيعات متقدمة
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="pages/customers/advanced-balance.php" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-balance-scale me-2"></i>
                            إدارة الأرصدة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-4">
            <h2>🎓 أصبحت خبيراً في نظام الرصيد المتقدم! 🎓</h2>
            <p class="lead">استمتع بإدارة احترافية ودقيقة للأرصدة والديون</p>
            
            <div class="alert alert-success mt-4">
                <h5><i class="fas fa-lightbulb text-warning me-2"></i>نصيحة ذهبية:</h5>
                <p class="mb-0">
                    <strong>المدين = عليه مال</strong> | <strong>الدائن = له مال</strong>
                    <br>هذا هو أساس فهم النظام المحاسبي الصحيح
                </p>
            </div>
            
            <a href="index.php" class="btn btn-light btn-lg mt-3">
                <i class="fas fa-home me-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
