<?php
/**
 * تطبيق تحديثات نظام الرصيد المتقدم
 * Apply Advanced Balance System Updates
 */

// استدعاء ملفات الإعدادات
require_once "config/db_config.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "تطبيق تحديثات نظام الرصيد المتقدم";
$updates_applied = false;
$errors = [];
$success_messages = [];

// معالجة تطبيق التحديثات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_updates'])) {
    try {
        // قراءة ملف التحديثات
        $sql_file = 'database/advanced-balance-system.sql';
        
        if (!file_exists($sql_file)) {
            throw new Exception("ملف التحديثات غير موجود: $sql_file");
        }
        
        $sql_content = file_get_contents($sql_file);
        
        if ($sql_content === false) {
            throw new Exception("فشل في قراءة ملف التحديثات");
        }
        
        // تقسيم الاستعلامات
        $queries = explode(';', $sql_content);
        
        $conn->begin_transaction();
        
        $executed_queries = 0;
        
        foreach ($queries as $query) {
            $query = trim($query);
            
            // تجاهل التعليقات والاستعلامات الفارغة
            if (empty($query) || 
                strpos($query, '--') === 0 || 
                strpos($query, '/*') === 0 ||
                strtoupper(substr($query, 0, 3)) === 'USE') {
                continue;
            }
            
            // تجاهل DELIMITER statements
            if (strpos(strtoupper($query), 'DELIMITER') === 0) {
                continue;
            }
            
            try {
                $result = $conn->query($query);
                if ($result === false) {
                    // تجاهل أخطاء الأعمدة الموجودة بالفعل
                    if (strpos($conn->error, 'Duplicate column name') !== false ||
                        strpos($conn->error, 'already exists') !== false) {
                        $success_messages[] = "تم تجاهل: " . substr($query, 0, 50) . "... (موجود بالفعل)";
                        continue;
                    }
                    throw new Exception("خطأ في الاستعلام: " . $conn->error . "\nالاستعلام: " . substr($query, 0, 100));
                }
                $executed_queries++;
                $success_messages[] = "تم تنفيذ: " . substr($query, 0, 50) . "...";
            } catch (Exception $e) {
                // تجاهل بعض الأخطاء المتوقعة
                if (strpos($e->getMessage(), 'Duplicate column name') !== false ||
                    strpos($e->getMessage(), 'already exists') !== false ||
                    strpos($e->getMessage(), 'Unknown column') !== false) {
                    $success_messages[] = "تم تجاهل: " . substr($query, 0, 50) . "... (موجود بالفعل أو غير مطلوب)";
                    continue;
                }
                throw $e;
            }
        }
        
        $conn->commit();
        
        $success_messages[] = "تم تطبيق جميع التحديثات بنجاح!";
        $success_messages[] = "عدد الاستعلامات المنفذة: $executed_queries";
        $updates_applied = true;
        
    } catch (Exception $e) {
        $conn->rollback();
        $errors[] = "خطأ في تطبيق التحديثات: " . $e->getMessage();
    }
}

// فحص حالة قاعدة البيانات
$database_status = [];

try {
    // فحص وجود الأعمدة الجديدة في جدول العملاء
    $check_customers = $conn->query("SHOW COLUMNS FROM customers LIKE 'wallet_balance'");
    $database_status['customers_wallet'] = $check_customers->num_rows > 0;
    
    $check_customers_debt = $conn->query("SHOW COLUMNS FROM customers LIKE 'debt_balance'");
    $database_status['customers_debt'] = $check_customers_debt->num_rows > 0;
    
    // فحص وجود جدول معاملات الرصيد
    $check_balance_transactions = $conn->query("SHOW TABLES LIKE 'balance_transactions'");
    $database_status['balance_transactions'] = $check_balance_transactions->num_rows > 0;
    
    // فحص وجود جدول إعدادات الرصيد
    $check_balance_settings = $conn->query("SHOW TABLES LIKE 'balance_settings'");
    $database_status['balance_settings'] = $check_balance_settings->num_rows > 0;
    
    // فحص وجود الإجراءات المخزنة
    $check_procedures = $conn->query("SHOW PROCEDURE STATUS WHERE Name = 'AddWalletBalance'");
    $database_status['procedures'] = $check_procedures->num_rows > 0;
    
} catch (Exception $e) {
    $errors[] = "خطأ في فحص قاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .status-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
        .status-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .status-danger {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-database me-3"></i>
                تطبيق تحديثات نظام الرصيد المتقدم
            </h1>
            <p class="lead">تحديث قاعدة البيانات لدعم نظام الرصيد المحفوظ والديون المتقدم</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($success_messages)): ?>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>رسائل النجاح:</h5>
                <ul class="mb-0">
                    <?php foreach ($success_messages as $message): ?>
                        <li><?php echo htmlspecialchars($message); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle me-2"></i>رسائل الخطأ:</h5>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- حالة قاعدة البيانات -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-database me-2"></i>حالة قاعدة البيانات</h3>
            </div>
            <div class="card-body">
                
                <div class="status-item <?php echo $database_status['customers_wallet'] ? 'status-success' : 'status-warning'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $database_status['customers_wallet'] ? 'check' : 'times'; ?> me-2"></i>
                        عمود الرصيد المحفوظ في جدول العملاء
                    </h6>
                    <p class="mb-0">
                        <?php echo $database_status['customers_wallet'] ? 'موجود ومُحدث' : 'غير موجود - يحتاج تحديث'; ?>
                    </p>
                </div>
                
                <div class="status-item <?php echo $database_status['customers_debt'] ? 'status-success' : 'status-warning'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $database_status['customers_debt'] ? 'check' : 'times'; ?> me-2"></i>
                        عمود رصيد الديون في جدول العملاء
                    </h6>
                    <p class="mb-0">
                        <?php echo $database_status['customers_debt'] ? 'موجود ومُحدث' : 'غير موجود - يحتاج تحديث'; ?>
                    </p>
                </div>
                
                <div class="status-item <?php echo $database_status['balance_transactions'] ? 'status-success' : 'status-warning'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $database_status['balance_transactions'] ? 'check' : 'times'; ?> me-2"></i>
                        جدول معاملات الرصيد
                    </h6>
                    <p class="mb-0">
                        <?php echo $database_status['balance_transactions'] ? 'موجود ومُحدث' : 'غير موجود - يحتاج إنشاء'; ?>
                    </p>
                </div>
                
                <div class="status-item <?php echo $database_status['balance_settings'] ? 'status-success' : 'status-warning'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $database_status['balance_settings'] ? 'check' : 'times'; ?> me-2"></i>
                        جدول إعدادات الرصيد
                    </h6>
                    <p class="mb-0">
                        <?php echo $database_status['balance_settings'] ? 'موجود ومُحدث' : 'غير موجود - يحتاج إنشاء'; ?>
                    </p>
                </div>
                
                <div class="status-item <?php echo $database_status['procedures'] ? 'status-success' : 'status-warning'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $database_status['procedures'] ? 'check' : 'times'; ?> me-2"></i>
                        الإجراءات المخزنة
                    </h6>
                    <p class="mb-0">
                        <?php echo $database_status['procedures'] ? 'موجودة ومُحدثة' : 'غير موجودة - تحتاج إنشاء'; ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- معلومات التحديث -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-info-circle me-2"></i>معلومات التحديث</h3>
            </div>
            <div class="card-body">
                <h5>ما الذي سيتم تحديثه؟</h5>
                <ul>
                    <li><strong>جدول العملاء:</strong> إضافة أعمدة الرصيد المحفوظ ورصيد الديون</li>
                    <li><strong>جدول الموردين:</strong> إضافة أعمدة الرصيد المحفوظ ورصيد الديون</li>
                    <li><strong>جدول معاملات الرصيد:</strong> جدول جديد لتتبع جميع معاملات الرصيد</li>
                    <li><strong>جدول إعدادات الرصيد:</strong> إعدادات نظام الرصيد المتقدم</li>
                    <li><strong>جدول المبيعات:</strong> إضافة أعمدة تفاصيل الدفع المتقدم</li>
                    <li><strong>جدول المشتريات:</strong> إضافة أعمدة تفاصيل الدفع المتقدم</li>
                    <li><strong>الإجراءات المخزنة:</strong> وظائف متقدمة لإدارة الرصيد</li>
                    <li><strong>الفيوز:</strong> عروض ملخصة لأرصدة العملاء والموردين</li>
                </ul>
                
                <div class="alert alert-warning mt-3">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيه مهم:</h6>
                    <p class="mb-0">
                        سيتم نقل البيانات الموجودة من العمود القديم <code>balance</code> إلى النظام الجديد تلقائياً.
                        <br>الأرصدة الموجبة ستصبح "رصيد محفوظ" والأرصدة السالبة ستصبح "ديون".
                    </p>
                </div>
            </div>
        </div>

        <!-- تطبيق التحديثات -->
        <?php if (!$updates_applied): ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-play me-2"></i>تطبيق التحديثات</h3>
                </div>
                <div class="card-body">
                    <p>انقر على الزر أدناه لتطبيق جميع التحديثات على قاعدة البيانات:</p>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من تطبيق التحديثات؟ هذه العملية لا يمكن التراجع عنها.')">
                        <button type="submit" name="apply_updates" class="btn btn-success btn-lg">
                            <i class="fas fa-database me-2"></i>
                            تطبيق التحديثات الآن
                        </button>
                    </form>
                </div>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-check-circle me-2"></i>تم تطبيق التحديثات بنجاح!</h3>
                </div>
                <div class="card-body">
                    <p>تم تطبيق جميع التحديثات بنجاح. يمكنك الآن استخدام نظام الرصيد المتقدم.</p>
                    
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="pages/sales/add-with-advanced-balance.php" class="btn btn-primary btn-lg me-md-2">
                            <i class="fas fa-cash-register me-2"></i>
                            تجربة نظام المبيعات المتقدم
                        </a>
                        <a href="pages/customers/advanced-balance.php" class="btn btn-info btn-lg me-md-2">
                            <i class="fas fa-balance-scale me-2"></i>
                            إدارة أرصدة العملاء
                        </a>
                        <a href="index.php" class="btn btn-secondary btn-lg">
                            <i class="fas fa-home me-2"></i>
                            العودة للرئيسية
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-4">
            <h2>🚀 نظام الرصيد المتقدم جاهز! 🚀</h2>
            <p class="lead">استمتع بإدارة متقدمة للأرصدة والديون</p>
            
            <div class="alert alert-info mt-4">
                <strong>ملاحظة:</strong> بعد تطبيق التحديثات، يمكنك حذف هذا الملف:
                <br><code>apply-advanced-balance-update.php</code>
            </div>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
