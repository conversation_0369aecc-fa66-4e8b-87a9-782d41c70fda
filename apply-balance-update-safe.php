<?php
/**
 * تطبيق تحديثات نظام الرصيد المتقدم - نسخة آمنة
 * Apply Advanced Balance System Updates - Safe Version
 */

// استدعاء ملفات الإعدادات
require_once "config/db_config.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "تطبيق تحديثات نظام الرصيد المتقدم - نسخة آمنة";
$updates_applied = false;
$errors = [];
$success_messages = [];

// معالجة تطبيق التحديثات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_updates'])) {
    try {
        $conn->begin_transaction();
        
        // 1. إضافة الأعمدة الجديدة للعملاء
        $customer_updates = [
            "ALTER TABLE `customers` ADD COLUMN `wallet_balance` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'الرصيد المحفوظ'",
            "ALTER TABLE `customers` ADD COLUMN `debt_balance` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'رصيد الديون'",
            "ALTER TABLE `customers` ADD COLUMN `credit_limit` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'حد الائتمان'",
            "ALTER TABLE `customers` ADD COLUMN `last_transaction_date` DATETIME NULL COMMENT 'تاريخ آخر معاملة'"
        ];
        
        foreach ($customer_updates as $query) {
            try {
                $conn->query($query);
                $success_messages[] = "تم تحديث جدول العملاء: " . substr($query, 0, 50) . "...";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                    $success_messages[] = "العمود موجود بالفعل في جدول العملاء";
                } else {
                    throw $e;
                }
            }
        }
        
        // 2. إضافة الأعمدة الجديدة للموردين
        $supplier_updates = [
            "ALTER TABLE `suppliers` ADD COLUMN `wallet_balance` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'الرصيد المحفوظ'",
            "ALTER TABLE `suppliers` ADD COLUMN `debt_balance` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'رصيد الديون'",
            "ALTER TABLE `suppliers` ADD COLUMN `credit_limit` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'حد الائتمان'",
            "ALTER TABLE `suppliers` ADD COLUMN `last_transaction_date` DATETIME NULL COMMENT 'تاريخ آخر معاملة'"
        ];
        
        foreach ($supplier_updates as $query) {
            try {
                $conn->query($query);
                $success_messages[] = "تم تحديث جدول الموردين: " . substr($query, 0, 50) . "...";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                    $success_messages[] = "العمود موجود بالفعل في جدول الموردين";
                } else {
                    throw $e;
                }
            }
        }
        
        // 3. إنشاء جدول معاملات الرصيد
        $balance_transactions_table = "
        CREATE TABLE IF NOT EXISTS `balance_transactions` (
          `id` INT(11) NOT NULL AUTO_INCREMENT,
          `entity_type` ENUM('customer', 'supplier') NOT NULL COMMENT 'نوع الكيان',
          `entity_id` INT(11) NOT NULL COMMENT 'معرف العميل أو المورد',
          `transaction_type` ENUM('wallet_add', 'wallet_use', 'debt_add', 'debt_pay', 'balance_transfer') NOT NULL COMMENT 'نوع المعاملة',
          `amount` DECIMAL(10,2) NOT NULL COMMENT 'المبلغ',
          `wallet_balance_before` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'الرصيد المحفوظ قبل المعاملة',
          `wallet_balance_after` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'الرصيد المحفوظ بعد المعاملة',
          `debt_balance_before` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'رصيد الديون قبل المعاملة',
          `debt_balance_after` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'رصيد الديون بعد المعاملة',
          `reference_type` ENUM('sale', 'purchase', 'payment', 'manual') NOT NULL COMMENT 'نوع المرجع',
          `reference_id` INT(11) NULL COMMENT 'معرف المرجع',
          `description` TEXT NULL COMMENT 'وصف المعاملة',
          `user_id` INT(11) NOT NULL COMMENT 'المستخدم الذي قام بالمعاملة',
          `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          INDEX `entity_idx` (`entity_type`, `entity_id`),
          INDEX `reference_idx` (`reference_type`, `reference_id`),
          INDEX `date_idx` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->query($balance_transactions_table);
        $success_messages[] = "تم إنشاء جدول معاملات الرصيد";
        
        // 4. إنشاء جدول إعدادات الرصيد
        $balance_settings_table = "
        CREATE TABLE IF NOT EXISTS `balance_settings` (
          `id` INT(11) NOT NULL AUTO_INCREMENT,
          `setting_key` VARCHAR(50) NOT NULL,
          `setting_value` TEXT NOT NULL,
          `description` TEXT NULL,
          PRIMARY KEY (`id`),
          UNIQUE KEY `setting_key` (`setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->query($balance_settings_table);
        $success_messages[] = "تم إنشاء جدول إعدادات الرصيد";
        
        // 5. إدراج الإعدادات الافتراضية
        $default_settings = [
            "INSERT IGNORE INTO `balance_settings` (`setting_key`, `setting_value`, `description`) VALUES ('min_wallet_usage', '10.00', 'الحد الأدنى للرصيد المحفوظ لعرض خيار الاستخدام')",
            "INSERT IGNORE INTO `balance_settings` (`setting_key`, `setting_value`, `description`) VALUES ('auto_debt_deduction', '1', 'خصم تلقائي من الرصيد المحفوظ عند تسجيل دين')",
            "INSERT IGNORE INTO `balance_settings` (`setting_key`, `setting_value`, `description`) VALUES ('wallet_notification_threshold', '50.00', 'حد التنبيه عند وصول الرصيد المحفوظ لمبلغ معين')",
            "INSERT IGNORE INTO `balance_settings` (`setting_key`, `setting_value`, `description`) VALUES ('default_credit_limit', '1000.00', 'حد الائتمان الافتراضي للعملاء الجدد')",
            "INSERT IGNORE INTO `balance_settings` (`setting_key`, `setting_value`, `description`) VALUES ('allow_negative_balance', '1', 'السماح بالرصيد السالب (الديون)')",
            "INSERT IGNORE INTO `balance_settings` (`setting_key`, `setting_value`, `description`) VALUES ('max_debt_days', '30', 'عدد الأيام القصوى للديون قبل التنبيه')"
        ];
        
        foreach ($default_settings as $query) {
            $conn->query($query);
        }
        $success_messages[] = "تم إدراج الإعدادات الافتراضية";
        
        // 6. إضافة أعمدة المبيعات المتقدمة
        $sales_updates = [
            "ALTER TABLE `sales` ADD COLUMN `wallet_used` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'المبلغ المستخدم من الرصيد المحفوظ'",
            "ALTER TABLE `sales` ADD COLUMN `cash_paid` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'المبلغ المدفوع نقداً'",
            "ALTER TABLE `sales` ADD COLUMN `change_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'مبلغ الفكة'",
            "ALTER TABLE `sales` ADD COLUMN `wallet_added` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'المبلغ المضاف للرصيد المحفوظ'",
            "ALTER TABLE `sales` ADD COLUMN `debt_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'مبلغ الدين المسجل'"
        ];
        
        foreach ($sales_updates as $query) {
            try {
                $conn->query($query);
                $success_messages[] = "تم تحديث جدول المبيعات: " . substr($query, 0, 50) . "...";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                    $success_messages[] = "العمود موجود بالفعل في جدول المبيعات";
                } else {
                    throw $e;
                }
            }
        }
        
        // 7. نقل البيانات الموجودة
        $migrate_customers = "UPDATE customers SET 
                             wallet_balance = CASE WHEN balance > 0 THEN balance ELSE 0 END,
                             debt_balance = CASE WHEN balance < 0 THEN ABS(balance) ELSE 0 END
                             WHERE balance != 0";
        $conn->query($migrate_customers);
        $success_messages[] = "تم نقل أرصدة العملاء الموجودة";
        
        $migrate_suppliers = "UPDATE suppliers SET 
                             wallet_balance = CASE WHEN balance > 0 THEN balance ELSE 0 END,
                             debt_balance = CASE WHEN balance < 0 THEN ABS(balance) ELSE 0 END
                             WHERE balance != 0";
        $conn->query($migrate_suppliers);
        $success_messages[] = "تم نقل أرصدة الموردين الموجودة";
        
        // 8. إنشاء المؤشرات (بطريقة آمنة)
        $indexes = [
            ['name' => 'idx_customers_wallet_balance', 'table' => 'customers', 'column' => 'wallet_balance'],
            ['name' => 'idx_customers_debt_balance', 'table' => 'customers', 'column' => 'debt_balance'],
            ['name' => 'idx_customers_last_transaction', 'table' => 'customers', 'column' => 'last_transaction_date'],
            ['name' => 'idx_suppliers_wallet_balance', 'table' => 'suppliers', 'column' => 'wallet_balance'],
            ['name' => 'idx_suppliers_debt_balance', 'table' => 'suppliers', 'column' => 'debt_balance'],
            ['name' => 'idx_suppliers_last_transaction', 'table' => 'suppliers', 'column' => 'last_transaction_date']
        ];

        foreach ($indexes as $index) {
            try {
                // فحص وجود المؤشر أولاً
                $check_index = "SHOW INDEX FROM {$index['table']} WHERE Key_name = '{$index['name']}'";
                $result = $conn->query($check_index);

                if ($result->num_rows == 0) {
                    // إنشاء المؤشر إذا لم يكن موجوداً
                    $create_index = "CREATE INDEX {$index['name']} ON {$index['table']}({$index['column']})";
                    $conn->query($create_index);
                    $success_messages[] = "تم إنشاء مؤشر: {$index['name']}";
                } else {
                    $success_messages[] = "المؤشر موجود بالفعل: {$index['name']}";
                }
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                    $success_messages[] = "المؤشر موجود بالفعل: {$index['name']}";
                } else {
                    $success_messages[] = "تحذير - لم يتم إنشاء المؤشر {$index['name']}: " . $e->getMessage();
                }
            }
        }
        
        $conn->commit();
        
        $success_messages[] = "تم تطبيق جميع التحديثات بنجاح!";
        $updates_applied = true;
        
    } catch (Exception $e) {
        $conn->rollback();
        $errors[] = "خطأ في تطبيق التحديثات: " . $e->getMessage();
    }
}

// فحص حالة قاعدة البيانات
$database_status = [];

try {
    // فحص وجود الأعمدة الجديدة
    $check_customers_wallet = $conn->query("SHOW COLUMNS FROM customers LIKE 'wallet_balance'");
    $database_status['customers_wallet'] = $check_customers_wallet->num_rows > 0;
    
    $check_customers_debt = $conn->query("SHOW COLUMNS FROM customers LIKE 'debt_balance'");
    $database_status['customers_debt'] = $check_customers_debt->num_rows > 0;
    
    $check_balance_transactions = $conn->query("SHOW TABLES LIKE 'balance_transactions'");
    $database_status['balance_transactions'] = $check_balance_transactions->num_rows > 0;
    
    $check_balance_settings = $conn->query("SHOW TABLES LIKE 'balance_settings'");
    $database_status['balance_settings'] = $check_balance_settings->num_rows > 0;
    
} catch (Exception $e) {
    $errors[] = "خطأ في فحص قاعدة البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .status-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
        .status-success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-shield-alt me-3"></i>
                تطبيق تحديثات نظام الرصيد - نسخة آمنة
            </h1>
            <p class="lead">تحديث آمن ومتدرج لقاعدة البيانات</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($success_messages)): ?>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>رسائل النجاح:</h5>
                <ul class="mb-0">
                    <?php foreach ($success_messages as $message): ?>
                        <li><?php echo htmlspecialchars($message); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle me-2"></i>رسائل الخطأ:</h5>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- حالة قاعدة البيانات -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-database me-2"></i>حالة قاعدة البيانات</h3>
            </div>
            <div class="card-body">
                
                <div class="status-item <?php echo $database_status['customers_wallet'] ? 'status-success' : 'status-warning'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $database_status['customers_wallet'] ? 'check' : 'times'; ?> me-2"></i>
                        أعمدة الرصيد في جدول العملاء
                    </h6>
                    <p class="mb-0">
                        <?php echo $database_status['customers_wallet'] ? 'موجودة ومُحدثة' : 'غير موجودة - تحتاج تحديث'; ?>
                    </p>
                </div>
                
                <div class="status-item <?php echo $database_status['balance_transactions'] ? 'status-success' : 'status-warning'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $database_status['balance_transactions'] ? 'check' : 'times'; ?> me-2"></i>
                        جدول معاملات الرصيد
                    </h6>
                    <p class="mb-0">
                        <?php echo $database_status['balance_transactions'] ? 'موجود ومُحدث' : 'غير موجود - يحتاج إنشاء'; ?>
                    </p>
                </div>
                
                <div class="status-item <?php echo $database_status['balance_settings'] ? 'status-success' : 'status-warning'; ?>">
                    <h6>
                        <i class="fas fa-<?php echo $database_status['balance_settings'] ? 'check' : 'times'; ?> me-2"></i>
                        جدول إعدادات الرصيد
                    </h6>
                    <p class="mb-0">
                        <?php echo $database_status['balance_settings'] ? 'موجود ومُحدث' : 'غير موجود - يحتاج إنشاء'; ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- تطبيق التحديثات -->
        <?php if (!$updates_applied): ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-play me-2"></i>تطبيق التحديثات الآمنة</h3>
                </div>
                <div class="card-body">
                    <p>هذه النسخة الآمنة تطبق التحديثات بطريقة متدرجة وتتعامل مع الأخطاء المحتملة:</p>
                    <ul>
                        <li>إضافة الأعمدة واحداً تلو الآخر</li>
                        <li>تجاهل الأعمدة الموجودة بالفعل</li>
                        <li>نقل البيانات الموجودة بأمان</li>
                        <li>إنشاء المؤشرات والجداول الجديدة</li>
                    </ul>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من تطبيق التحديثات؟')">
                        <button type="submit" name="apply_updates" class="btn btn-success btn-lg">
                            <i class="fas fa-shield-alt me-2"></i>
                            تطبيق التحديثات الآمنة
                        </button>
                    </form>
                </div>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-check-circle me-2"></i>تم تطبيق التحديثات بنجاح!</h3>
                </div>
                <div class="card-body">
                    <p>تم تطبيق جميع التحديثات بنجاح. يمكنك الآن استخدام نظام الرصيد المتقدم.</p>
                    
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="pages/sales/add-with-advanced-balance.php" class="btn btn-primary btn-lg me-md-2">
                            <i class="fas fa-cash-register me-2"></i>
                            تجربة نظام المبيعات المتقدم
                        </a>
                        <a href="pages/customers/advanced-balance.php" class="btn btn-info btn-lg me-md-2">
                            <i class="fas fa-balance-scale me-2"></i>
                            إدارة أرصدة العملاء
                        </a>
                        <a href="index.php" class="btn btn-secondary btn-lg">
                            <i class="fas fa-home me-2"></i>
                            العودة للرئيسية
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-4">
            <h2>🛡️ تحديث آمن ومضمون! 🛡️</h2>
            <p class="lead">نسخة محسنة تتعامل مع جميع المشاكل المحتملة</p>
            
            <div class="alert alert-info mt-4">
                <strong>ملاحظة:</strong> هذه النسخة الآمنة تتعامل مع:
                <br>• الأعمدة الموجودة بالفعل
                <br>• الأخطاء المحتملة في قاعدة البيانات
                <br>• نقل البيانات الموجودة بأمان
            </div>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
