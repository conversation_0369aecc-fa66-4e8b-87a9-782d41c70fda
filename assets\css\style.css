/*
* نظام Zero لإدارة المحلات - ملف الأنماط الرئيسي
* Zero Store Management System - Main CSS file
*/

/* ألوان المنتجات */
.text-purple {
    color: #6f42c1 !important;
}

.btn-purple {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: white;
}

.btn-purple:hover {
    background-color: #5a359a;
    border-color: #5a359a;
    color: white;
}

/* تعريفات عامة */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* نمط عام للنص */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f5f5f5;
}

/* العناوين */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

/* عنوان الصفحة */
.page-title {
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

/* بطاقات المعلومات */
.info-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    margin-bottom: 20px;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.info-card-header {
    padding: 15px;
    border-radius: 10px 10px 0 0;
    color: white;
}

.info-card-body {
    padding: 20px;
}

.info-card-footer {
    padding: 10px 15px;
    background-color: rgba(0, 0, 0, 0.05);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0 0 10px 10px;
}

/* لوحة الإحصائيات */
.stats-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.stats-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.stats-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-label {
    color: var(--secondary-color);
    font-size: 1rem;
}

/* جداول البيانات */
.data-table {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.data-table .table {
    margin-bottom: 0;
}

.data-table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
}

.data-table .table-actions {
    width: 150px;
    text-align: center;
}

/* أزرار الإجراءات */
.action-buttons .btn {
    margin: 0 2px;
    padding: 5px 10px;
    font-size: 0.85rem;
}

/* نماذج الإدخال */
.form-control {
    border-radius: 5px;
    padding: 8px 12px;
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 25px;
    margin-bottom: 30px;
}

/* رسائل التنبيه */
.alert {
    border-radius: 8px;
    border: none;
    padding: 15px;
    margin-bottom: 20px;
}

/* تخصيص شريط التنقل */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.3rem;
}

.nav-link {
    font-weight: 500;
    padding: 10px 15px;
    transition: color 0.3s;
}

/* تصميم صفحة تسجيل الدخول */
.login-container {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-card {
    width: 400px;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    background-color: white;
}

.login-logo {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo i {
    font-size: 4rem;
    color: var(--primary-color);
}

/* تصميم للطباعة */
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }

    body {
        padding: 0;
        margin: 0;
    }

    .container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
    }

    .print-header {
        text-align: center;
        margin-bottom: 20px;
    }

    .print-title {
        font-size: 24px;
        margin-bottom: 10px;
    }
}

/* تصميم متجاوب للشاشات الصغيرة */
@media (max-width: 767.98px) {
    .stats-card {
        margin-bottom: 15px;
    }

    .stats-value {
        font-size: 1.5rem;
    }

    .data-table {
        overflow-x: auto;
    }

    .form-card {
        padding: 15px;
    }
}

/* تنسيق المحتويات بالعربية */
[dir="rtl"] .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

[dir="rtl"] .dropdown-menu-end {
    left: 0 !important;
    right: auto !important;
}

/* تنسيق عناصر الفواتير */
.invoice-header {
    border-bottom: 2px solid #eee;
    margin-bottom: 20px;
    padding-bottom: 20px;
}

.invoice-title {
    font-size: 1.8rem;
    font-weight: 700;
}

.invoice-details {
    margin-bottom: 30px;
}

.invoice-details-row {
    margin-bottom: 8px;
}

.invoice-total {
    font-size: 1.2rem;
    font-weight: 700;
    border-top: 2px solid #eee;
    padding-top: 15px;
    margin-top: 15px;
}

/* تنسيق لوحة التحكم الرئيسية */
.dashboard-section {
    margin-bottom: 40px;
}

.dashboard-section-title {
    margin-bottom: 20px;
    position: relative;
    padding-right: 15px;
    display: inline-block;
}

.dashboard-section-title:before {
    content: "";
    position: absolute;
    right: 0;
    top: 10%;
    width: 4px;
    height: 80%;
    background-color: var(--primary-color);
    border-radius: 2px;
}
