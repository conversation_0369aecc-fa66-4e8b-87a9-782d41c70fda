/* 
* نظام Zero لإدارة المحلات - ملف الجافاسكريبت الرئيسي 
* Zero Store Management System - Main JavaScript file
*/

// تنفيذ الكود عند اكتمال تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('نظام Zero جاهز للاستخدام');

    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // تفعيل popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // تهيئة أي عناصر تاريخ
    setupDatepickers();

    // تهيئة عناصر اختيار المنتجات
    setupProductSelectors();

    // تهيئة أحداث الحذف للمساعدة في تأكيد الحذف
    setupDeleteConfirmation();
});

/**
 * تهيئة مربعات اختيار التاريخ
 */
function setupDatepickers() {
    var datepickers = document.querySelectorAll('.datepicker');
    if (datepickers.length > 0) {
        datepickers.forEach(function(picker) {
            // يتم تنفيذ هذا الكود فقط إذا تم استخدام مكتبة datepicker
            if (typeof flatpickr !== 'undefined') {
                flatpickr(picker, {
                    dateFormat: "Y-m-d",
                    locale: "ar"
                });
            }
        });
    }
}

/**
 * تهيئة مربعات اختيار المنتجات
 */
function setupProductSelectors() {
    var productSelectors = document.querySelectorAll('.product-selector');
    if (productSelectors.length > 0) {
        productSelectors.forEach(function(selector) {
            // يتم تنفيذ هذا الكود فقط إذا تم استخدام مكتبة select2
            if (typeof $(selector).select2 !== 'undefined') {
                $(selector).select2({
                    placeholder: "اختر المنتج",
                    allowClear: true,
                    dir: "rtl"
                });

                // عند تغيير المنتج، استرجاع معلومات السعر والكمية
                $(selector).on('change', function() {
                    var productId = $(this).val();
                    if (productId) {
                        fetchProductDetails(productId, this);
                    } else {
                        resetProductFields(this);
                    }
                });
            }
        });
    }
}

/**
 * استرجاع تفاصيل المنتج عبر AJAX
 * @param {number} productId معرف المنتج
 * @param {HTMLElement} element عنصر مربع الاختيار
 */
function fetchProductDetails(productId, element) {
    // البحث عن الصف الذي يحتوي على العنصر
    var row = $(element).closest('tr');

    // عرض حالة التحميل
    row.find('.loading-spinner').show();

    // إرسال طلب AJAX للحصول على تفاصيل المنتج
    $.ajax({
        url: 'get_product_details.php',
        type: 'GET',
        data: { product_id: productId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // تحديث حقول السعر والكمية المتاحة
                row.find('.product-price').val(response.data.selling_price);
                row.find('.available-qty').text(response.data.quantity);
                row.find('.product-qty').attr('max', response.data.quantity);

                // إعادة حساب المجموع
                calculateRowTotal(row);
            } else {
                showAlert('خطأ', 'حدث خطأ أثناء استرجاع تفاصيل المنتج', 'danger');
            }
        },
        error: function() {
            showAlert('خطأ', 'حدث خطأ في الاتصال بالخادم', 'danger');
        },
        complete: function() {
            // إخفاء حالة التحميل
            row.find('.loading-spinner').hide();
        }
    });
}

/**
 * إعادة تعيين حقول المنتج
 * @param {HTMLElement} element عنصر مربع الاختيار
 */
function resetProductFields(element) {
    var row = $(element).closest('tr');
    row.find('.product-price').val('');
    row.find('.available-qty').text('0');
    row.find('.product-qty').val('1').attr('max', '');
    calculateRowTotal(row);
}

/**
 * حساب مجموع الصف
 * @param {HTMLElement} row صف الجدول
 */
function calculateRowTotal(row) {
    var price = parseFloat($(row).find('.product-price').val()) || 0;
    var qty = parseInt($(row).find('.product-qty').val()) || 0;
    var total = price * qty;

    // تحديث حقل المجموع
    $(row).find('.row-total').val(total.toFixed(2));

    // إعادة حساب المجموع الكلي للفاتورة
    calculateInvoiceTotal();
}

/**
 * حساب مجموع الفاتورة
 */
function calculateInvoiceTotal() {
    var subTotal = 0;

    // حساب مجموع كل الصفوف
    $('.row-total').each(function() {
        subTotal += parseFloat($(this).val()) || 0;
    });

    // الحصول على قيم الخصم
    var discount = parseFloat($('#discount').val()) || 0;

    // حساب المجموع النهائي
    var total = subTotal - discount;

    // تحديث حقول المجاميع
    $('#subtotal').text(subTotal.toFixed(2));
    $('#total').text(total.toFixed(2));

    // تحديث قيمة المدفوع
    updatePaidAndRemaining();
}

/**
 * تحديث المبلغ المدفوع والباقي
 */
function updatePaidAndRemaining() {
    var total = parseFloat($('#total').text()) || 0;
    var paid = parseFloat($('#paid_amount').val()) || 0;
    var remaining = total - paid;

    // تحديث حقل المبلغ المتبقي
    $('#remaining').text(remaining.toFixed(2));
}

/**
 * إضافة صف جديد للفاتورة
 */
function addNewInvoiceRow() {
    var rowTemplate = $('#row-template').html();
    var rowCount = $('.invoice-item-row').length + 1;

    // استبدال قيمة الفهرس في القالب
    rowTemplate = rowTemplate.replace(/\{index\}/g, rowCount);

    // إضافة الصف الجديد
    $('#invoice-items-container').append(rowTemplate);

    // تهيئة الصف الجديد
    setupProductSelectors();
    setupRowEvents();
}

/**
 * تهيئة أحداث الصفوف
 */
function setupRowEvents() {
    // حدث تغيير الكمية
    $('.product-qty').off('change').on('change', function() {
        var row = $(this).closest('tr');
        calculateRowTotal(row);
    });

    // حدث تغيير السعر
    $('.product-price').off('change').on('change', function() {
        var row = $(this).closest('tr');
        calculateRowTotal(row);
    });

    // حدث حذف الصف
    $('.delete-row').off('click').on('click', function() {
        $(this).closest('tr').remove();
        calculateInvoiceTotal();
    });
}

/**
 * تهيئة تأكيدات الحذف
 */
function setupDeleteConfirmation() {
    $('.delete-btn').on('click', function(e) {
        e.preventDefault();
        var url = $(this).attr('href');
        var name = $(this).data('name');

        $('#deleteModal .item-name').text(name || 'العنصر');
        $('#confirmDeleteBtn').attr('href', url);

        var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        deleteModal.show();
    });
}

/**
 * عرض تنبيه للمستخدم
 * @param {string} title عنوان التنبيه
 * @param {string} message نص التنبيه
 * @param {string} type نوع التنبيه (success, danger, warning, info)
 */
function showAlert(title, message, type) {
    // إنشاء عنصر التنبيه
    var alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-' + type + ' alert-dismissible fade show';
    alertDiv.role = 'alert';

    var content = '';
    if (title) {
        content += '<strong>' + title + '</strong>: ';
    }
    content += message;

    content += '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>';

    alertDiv.innerHTML = content;

    // إضافة التنبيه إلى الصفحة
    var alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alertContainer';
        alertContainer.className = 'alert-container';
        document.body.insertBefore(alertContainer, document.body.firstChild);
    }

    alertContainer.appendChild(alertDiv);

    // إخفاء التنبيه تلقائيًا بعد 5 ثوان
    setTimeout(function() {
        $(alertDiv).alert('close');
    }, 5000);
}

/**
 * طباعة الفاتورة
 * @param {string} elementId معرف العنصر المراد طباعته
 */
function printInvoice(elementId) {
    var printContents = document.getElementById(elementId).innerHTML;
    var originalContents = document.body.innerHTML;

    document.body.innerHTML = printContents;
    window.print();
    document.body.innerHTML = originalContents;

    // إعادة تهيئة السكريبت بعد إعادة المحتوى الأصلي
    document.addEventListener('DOMContentLoaded', function() {
        setupDatepickers();
        setupProductSelectors();
        setupDeleteConfirmation();
    });
}

/**
 * تحميل بيانات من الخادم بواسطة AJAX
 * @param {string} url عنوان URL للطلب
 * @param {Object} params معاملات الطلب
 * @param {function} successCallback دالة النجاح
 * @param {function} errorCallback دالة الفشل
 */
function loadData(url, params, successCallback, errorCallback) {
    $.ajax({
        url: url,
        type: 'GET',
        data: params,
        dataType: 'json',
        success: function(response) {
            if (successCallback && typeof successCallback === 'function') {
                successCallback(response);
            }
        },
        error: function(xhr, status, error) {
            if (errorCallback && typeof errorCallback === 'function') {
                errorCallback(xhr, status, error);
            } else {
                showAlert('خطأ', 'حدث خطأ أثناء تحميل البيانات', 'danger');
            }
        }
    });
}
