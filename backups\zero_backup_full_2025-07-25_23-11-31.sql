-- نسخة احتياطية من قاعدة بيانات Zero
-- تاريخ الإنشاء: 2025-07-25 23:11:31
-- نوع النسخة: full

SET FOREIGN_KEY_CHECKS=0;

-- ه<PERSON><PERSON><PERSON> الجدول `categories`
DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `parent_id` int DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `parent_id` (`parent_id`),
  KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `categories`
INSERT INTO `categories` VALUES ('1', 'عام', 'فئة عامة للمنتجات', NULL, '1', '2025-07-25 22:35:36');
INSERT INTO `categories` VALUES ('2', 'مواد غذائية', 'المواد الغذائية والمشروبات', NULL, '1', '2025-07-25 23:00:10');
INSERT INTO `categories` VALUES ('3', 'إلكترونيات', 'الأجهزة الإلكترونية والكهربائية', NULL, '1', '2025-07-25 23:00:10');
INSERT INTO `categories` VALUES ('4', 'ملابس', 'الملابس والأزياء', NULL, '1', '2025-07-25 23:00:10');
INSERT INTO `categories` VALUES ('5', 'مستلزمات منزلية', 'أدوات ومستلزمات المنزل', NULL, '1', '2025-07-25 23:00:10');
INSERT INTO `categories` VALUES ('6', 'قرطاسية', 'الأدوات المكتبية والقرطاسية', NULL, '1', '2025-07-25 23:00:10');

-- هيكل الجدول `customers`
DROP TABLE IF EXISTS `customers`;
CREATE TABLE `customers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `balance` decimal(10,2) DEFAULT '0.00',
  `credit_limit` decimal(10,2) DEFAULT '0.00',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `phone` (`phone`),
  KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `customers`
INSERT INTO `customers` VALUES ('1', 'عميل نقدي', '', NULL, 'غير محدد', '0.00', '0.00', '1', '2025-07-25 22:35:36', '2025-07-25 22:35:36');
INSERT INTO `customers` VALUES ('2', 'أحمد محمد', '0501234567', '<EMAIL>', 'الرياض - حي النخيل', '4200.00', '0.00', '1', '2025-07-25 23:00:10', '2025-07-25 23:02:00');
INSERT INTO `customers` VALUES ('3', 'فاطمة علي', '0507654321', '<EMAIL>', 'جدة - حي الصفا', '0.00', '0.00', '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');
INSERT INTO `customers` VALUES ('4', 'محمد سالم', '0551234567', '<EMAIL>', 'الدمام - حي الفيصلية', '0.00', '0.00', '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');
INSERT INTO `customers` VALUES ('5', 'نورا أحمد', '0561234567', '<EMAIL>', 'مكة - حي العزيزية', '0.00', '0.00', '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');
INSERT INTO `customers` VALUES ('6', 'خالد عبدالله', '0581234567', '<EMAIL>', 'المدينة - حي قباء', '0.00', '0.00', '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');

-- هيكل الجدول `expenses`
DROP TABLE IF EXISTS `expenses`;
CREATE TABLE `expenses` (
  `id` int NOT NULL AUTO_INCREMENT,
  `expense_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `user_id` int NOT NULL,
  `expense_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `expenses`
INSERT INTO `expenses` VALUES ('1', 'إيجار', '', '2000.00', '1', '2025-01-01', '2025-07-26 00:05:49', 'إيجار المحل الشهري');
INSERT INTO `expenses` VALUES ('2', 'كهرباء', '', '350.50', '1', '2025-01-05', '2025-07-26 00:05:49', 'فاتورة الكهرباء');
INSERT INTO `expenses` VALUES ('3', 'مياه', '', '120.00', '1', '2025-01-05', '2025-07-26 00:05:49', 'فاتورة المياه');
INSERT INTO `expenses` VALUES ('4', 'هاتف وإنترنت', '', '200.00', '1', '2025-01-10', '2025-07-26 00:05:49', 'فاتورة الاتصالات');
INSERT INTO `expenses` VALUES ('5', 'صيانة', '', '150.00', '1', '2025-01-15', '2025-07-26 00:05:49', 'صيانة المعدات');

-- هيكل الجدول `products`
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `barcode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category_id` int DEFAULT '1',
  `cost_price` decimal(10,2) DEFAULT '0.00',
  `selling_price` decimal(10,2) NOT NULL,
  `stock_quantity` decimal(10,3) DEFAULT '0.000',
  `min_stock` decimal(10,3) DEFAULT '0.000',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'قطعة',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `barcode` (`barcode`),
  KEY `category_id` (`category_id`),
  KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `products`
INSERT INTO `products` VALUES ('1', 'أرز بسمتي 5 كيلو', 'RICE001', '1', '25.00', '35.00', '99.000', '5.000', 'كيس', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-26 01:00:05');
INSERT INTO `products` VALUES ('2', 'زيت زيتون 500 مل', 'OIL001', '1', '15.00', '25.00', '49.000', '5.000', 'زجاجة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-26 01:00:05');
INSERT INTO `products` VALUES ('3', 'سكر أبيض 2 كيلو', 'SUGAR001', '1', '8.00', '12.00', '79.000', '5.000', 'كيس', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-26 01:00:05');
INSERT INTO `products` VALUES ('4', 'شاي أحمد 400 جرام', 'TEA001', '1', '12.00', '18.00', '60.000', '5.000', 'علبة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');
INSERT INTO `products` VALUES ('5', 'قهوة عربية 500 جرام', 'COFFEE001', '1', '20.00', '30.00', '-60.000', '5.000', 'علبة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-25 23:01:59');
INSERT INTO `products` VALUES ('6', 'سماعات بلوتوث', 'HEADPHONE001', '2', '80.00', '120.00', '22.000', '5.000', 'قطعة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-26 01:00:05');
INSERT INTO `products` VALUES ('7', 'شاحن جوال سريع', 'CHARGER001', '2', '25.00', '40.00', '49.000', '5.000', 'قطعة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-26 01:00:05');
INSERT INTO `products` VALUES ('8', 'كابل USB نوع C', 'CABLE001', '2', '15.00', '25.00', '90.000', '5.000', 'قطعة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-25 23:08:22');
INSERT INTO `products` VALUES ('9', 'بطارية محمولة 10000', 'POWERBANK001', '2', '60.00', '90.00', '29.000', '5.000', 'قطعة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-26 01:00:05');
INSERT INTO `products` VALUES ('10', 'قميص رجالي قطني', 'SHIRT001', '3', '40.00', '65.00', '20.000', '5.000', 'قطعة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');
INSERT INTO `products` VALUES ('11', 'بنطلون جينز', 'JEANS001', '3', '80.00', '120.00', '15.000', '5.000', 'قطعة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');
INSERT INTO `products` VALUES ('12', 'فستان نسائي', 'DRESS001', '3', '60.00', '95.00', '12.000', '5.000', 'قطعة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');
INSERT INTO `products` VALUES ('13', 'مقلاة تيفال 28 سم', 'PAN001', '4', '45.00', '70.00', '20.000', '5.000', 'قطعة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');
INSERT INTO `products` VALUES ('14', 'طقم أكواب زجاج', 'GLASSES001', '4', '25.00', '40.00', '30.000', '5.000', 'طقم', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');
INSERT INTO `products` VALUES ('15', 'منشفة قطنية كبيرة', 'TOWEL001', '4', '20.00', '35.00', '40.000', '5.000', 'قطعة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');
INSERT INTO `products` VALUES ('16', 'دفتر 100 ورقة', 'NOTEBOOK001', '5', '3.00', '5.00', '200.000', '5.000', 'قطعة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');
INSERT INTO `products` VALUES ('17', 'قلم حبر جاف أزرق', 'PEN001', '5', '1.00', '2.00', '500.000', '5.000', 'قطعة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');
INSERT INTO `products` VALUES ('18', 'مجلد ملفات A4', 'FOLDER001', '5', '8.00', '12.00', '0.000', '5.000', 'قطعة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-25 23:02:00');
INSERT INTO `products` VALUES ('19', 'آلة حاسبة علمية', 'CALC001', '5', '35.00', '55.00', '24.000', '5.000', 'قطعة', NULL, NULL, '1', '2025-07-25 23:00:10', '2025-07-26 01:00:05');
INSERT INTO `products` VALUES ('20', 'رز مصر', '1752850951911', '2', '30.00', '35.00', '100.000', '5.000', 'عبوة', '', NULL, '1', '2025-07-25 23:43:24', '2025-07-25 23:43:24');

-- هيكل الجدول `purchase_items`
DROP TABLE IF EXISTS `purchase_items`;
CREATE TABLE `purchase_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `purchase_id` int NOT NULL,
  `product_id` int NOT NULL,
  `quantity` decimal(10,3) NOT NULL,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `discount_amount` decimal(10,2) DEFAULT '0.00',
  PRIMARY KEY (`id`),
  KEY `purchase_id` (`purchase_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- هيكل الجدول `purchases`
DROP TABLE IF EXISTS `purchases`;
CREATE TABLE `purchases` (
  `id` int NOT NULL AUTO_INCREMENT,
  `purchase_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `supplier_id` int DEFAULT NULL,
  `user_id` int NOT NULL,
  `purchase_date` date NOT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `discount_amount` decimal(10,2) DEFAULT '0.00',
  `tax_amount` decimal(10,2) DEFAULT '0.00',
  `final_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `paid_amount` decimal(10,2) DEFAULT '0.00',
  `payment_method` enum('cash','card','bank_transfer') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'cash',
  `payment_status` enum('paid','unpaid','partial') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'paid',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `purchase_number` (`purchase_number`),
  KEY `supplier_id` (`supplier_id`),
  KEY `user_id` (`user_id`),
  KEY `purchase_date` (`purchase_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- هيكل الجدول `sale_items`
DROP TABLE IF EXISTS `sale_items`;
CREATE TABLE `sale_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `sale_id` int NOT NULL,
  `product_id` int NOT NULL,
  `quantity` decimal(10,3) NOT NULL,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `discount_amount` decimal(10,2) DEFAULT '0.00',
  PRIMARY KEY (`id`),
  KEY `sale_id` (`sale_id`),
  KEY `product_id` (`product_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `sale_items`
INSERT INTO `sale_items` VALUES ('1', '1', '5', '100.000', '30.00', '3000.00', '0.00');
INSERT INTO `sale_items` VALUES ('2', '1', '18', '100.000', '12.00', '1200.00', '0.00');
INSERT INTO `sale_items` VALUES ('3', '2', '8', '10.000', '25.00', '250.00', '0.00');
INSERT INTO `sale_items` VALUES ('4', '2', '6', '2.000', '120.00', '240.00', '0.00');
INSERT INTO `sale_items` VALUES ('5', '3', '9', '1.000', '90.00', '90.00', '0.00');
INSERT INTO `sale_items` VALUES ('6', '3', '1', '1.000', '35.00', '35.00', '0.00');
INSERT INTO `sale_items` VALUES ('7', '3', '19', '1.000', '55.00', '55.00', '0.00');
INSERT INTO `sale_items` VALUES ('8', '3', '2', '1.000', '25.00', '25.00', '0.00');
INSERT INTO `sale_items` VALUES ('9', '3', '3', '1.000', '12.00', '12.00', '0.00');
INSERT INTO `sale_items` VALUES ('10', '3', '6', '1.000', '120.00', '120.00', '0.00');
INSERT INTO `sale_items` VALUES ('11', '3', '7', '1.000', '40.00', '40.00', '0.00');

-- هيكل الجدول `sales`
DROP TABLE IF EXISTS `sales`;
CREATE TABLE `sales` (
  `id` int NOT NULL AUTO_INCREMENT,
  `sale_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `customer_id` int DEFAULT '1',
  `user_id` int NOT NULL,
  `sale_date` date NOT NULL,
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `discount_amount` decimal(10,2) DEFAULT '0.00',
  `tax_amount` decimal(10,2) DEFAULT '0.00',
  `final_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `paid_amount` decimal(10,2) DEFAULT '0.00',
  `payment_method` enum('cash','card','bank_transfer') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'cash',
  `payment_status` enum('paid','unpaid','partial') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'paid',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sale_number` (`sale_number`),
  KEY `customer_id` (`customer_id`),
  KEY `user_id` (`user_id`),
  KEY `sale_date` (`sale_date`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `sales`
INSERT INTO `sales` VALUES ('1', 'S202507250001', '2', '1', '2025-07-25', '4200.00', '0.00', '0.00', '4200.00', '0.00', 'cash', 'paid', '', '2025-07-25 23:01:59');
INSERT INTO `sales` VALUES ('2', 'S202507250002', '2', '1', '2025-07-25', '490.00', '0.00', '0.00', '490.00', '5000.00', 'cash', 'paid', '', '2025-07-25 23:08:22');
INSERT INTO `sales` VALUES ('3', 'S2025000003', '2', '1', '2025-07-25', '377.00', '0.00', '0.00', '377.00', '380.00', 'cash', 'paid', '', '2025-07-26 01:00:05');

-- هيكل الجدول `settings`
DROP TABLE IF EXISTS `settings`;
CREATE TABLE `settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_type` enum('text','number','boolean','json') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'text',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `settings`
INSERT INTO `settings` VALUES ('1', 'store_name', 'النسور الذهبية', 'text', 'اسم المتجر', '2025-07-25 22:48:51', '2025-07-26 00:14:14');
INSERT INTO `settings` VALUES ('2', 'store_currency', 'جنيه', 'text', 'عملة المتجر', '2025-07-25 22:48:51', '2025-07-26 00:14:14');
INSERT INTO `settings` VALUES ('3', 'store_address', 'دمياط', 'text', 'عنوان المتجر', '2025-07-25 22:48:51', '2025-07-26 00:14:14');
INSERT INTO `settings` VALUES ('4', 'store_phone', '01032547745', 'text', 'هاتف المتجر', '2025-07-25 22:48:51', '2025-07-26 00:14:14');
INSERT INTO `settings` VALUES ('5', 'store_email', '<EMAIL>', 'text', 'بريد المتجر الإلكتروني', '2025-07-25 22:48:51', '2025-07-26 00:14:14');
INSERT INTO `settings` VALUES ('6', 'tax_rate', '0', 'number', 'معدل الضريبة (%)', '2025-07-25 22:48:51', '2025-07-26 00:14:14');
INSERT INTO `settings` VALUES ('7', 'low_stock_alert', '5', 'number', 'تنبيه المخزون المنخفض', '2025-07-25 22:48:51', '2025-07-25 22:48:51');
INSERT INTO `settings` VALUES ('8', 'receipt_footer', 'شكراً لزيارتكم', 'text', 'تذييل الفاتورة', '2025-07-25 22:48:51', '2025-07-25 22:48:51');
INSERT INTO `settings` VALUES ('9', 'auto_backup', '1', 'boolean', 'النسخ الاحتياطي التلقائي', '2025-07-25 22:48:51', '2025-07-25 22:48:51');
INSERT INTO `settings` VALUES ('10', 'theme_color', 'blue', 'text', 'لون النظام', '2025-07-25 22:48:51', '2025-07-25 22:48:51');
INSERT INTO `settings` VALUES ('11', 'default_payment_method', 'cash', 'text', NULL, '2025-07-26 00:14:14', '2025-07-26 00:14:14');
INSERT INTO `settings` VALUES ('12', 'low_stock_threshold', '5', 'text', NULL, '2025-07-26 00:14:14', '2025-07-26 00:14:14');
INSERT INTO `settings` VALUES ('13', 'backup_frequency', 'weekly', 'text', NULL, '2025-07-26 00:14:14', '2025-07-26 00:14:14');
INSERT INTO `settings` VALUES ('14', 'enable_notifications', '1', 'text', NULL, '2025-07-26 00:14:14', '2025-07-26 00:14:14');
INSERT INTO `settings` VALUES ('15', 'enable_barcode_scanner', '0', 'text', NULL, '2025-07-26 00:14:14', '2025-07-26 00:14:14');

-- هيكل الجدول `suppliers`
DROP TABLE IF EXISTS `suppliers`;
CREATE TABLE `suppliers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `company` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `balance` decimal(10,2) DEFAULT '0.00',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `phone` (`phone`),
  KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `suppliers`
INSERT INTO `suppliers` VALUES ('1', 'شركة الغذاء المتميز', 'مؤسسة الغذاء', '0112345678', '<EMAIL>', 'الرياض - المنطقة الصناعية', '0.00', '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');
INSERT INTO `suppliers` VALUES ('2', 'مؤسسة التقنية الحديثة', 'التقنية الحديثة', '0123456789', '<EMAIL>', 'جدة - حي الروضة', '0.00', '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');
INSERT INTO `suppliers` VALUES ('3', 'شركة النسيج الذهبي', 'النسيج الذهبي', '0134567890', '<EMAIL>', 'الدمام - المنطقة التجارية', '0.00', '1', '2025-07-25 23:00:10', '2025-07-25 23:00:10');

-- هيكل الجدول `treasury`
DROP TABLE IF EXISTS `treasury`;
CREATE TABLE `treasury` (
  `id` int NOT NULL AUTO_INCREMENT,
  `transaction_type` enum('sales','purchases','expenses','deposit','withdraw') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `reference_id` int DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `balance_after` decimal(10,2) NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `user_id` int NOT NULL,
  `transaction_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `treasury`
INSERT INTO `treasury` VALUES ('1', 'deposit', NULL, '0.00', '0.00', 'رصيد افتراضي', '1', '2025-07-25', '2025-07-25 22:32:43');
INSERT INTO `treasury` VALUES ('2', 'sales', '2', '5000.00', '5000.00', 'مبيعة رقم: S202507250002', '1', '0000-00-00', '2025-07-25 23:08:22');
INSERT INTO `treasury` VALUES ('3', 'withdraw', NULL, '-310.00', '4690.00', 'باقي حساب', '1', '2025-07-25', '2025-07-26 00:27:52');
INSERT INTO `treasury` VALUES ('4', 'sales', '3', '380.00', '5070.00', 'مبيعة رقم: S2025000003', '1', '2025-07-25', '2025-07-26 01:00:05');

-- هيكل الجدول `users`
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` enum('admin','cashier','manager') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- بيانات الجدول `users`
INSERT INTO `users` VALUES ('1', 'admin', '$2y$10$.nxYBJvP1aapXkj.c2KGEeEVroi6NOcDy.e3jxlgDxmBmMixlmcS.', 'مدير النظام', 'admin', '2025-07-25 22:41:47', 'active', NULL, NULL, '2025-07-26 01:54:29');
INSERT INTO `users` VALUES ('2', 'admin123', '$2y$10$etasC1AEP7nA259u8IJ.u.XHe7BB5eObOTJW8SNAC0tH5mgaQP1aC', 'Ahmed Moussa', 'cashier', '2025-07-26 01:55:22', 'active', '<EMAIL>', '01032547745', '2025-07-26 01:55:22');

SET FOREIGN_KEY_CHECKS=1;
