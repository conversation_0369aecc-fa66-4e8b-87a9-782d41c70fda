<?php
/**
 * فحص صلاحية المستخدم الحالي
 * Check Current User Role
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "config/db_config.php";
require_once "includes/functions.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص صلاحية المستخدم - نظام Zero</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-user-shield me-3"></i>
                فحص صلاحية المستخدم
            </h1>
            <p class="lead">تشخيص مشكلة الوصول لإعادة ضبط المصنع</p>
        </div>

        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-info-circle me-2"></i>معلومات الجلسة الحالية</h3>
            </div>
            <div class="card-body">
                <?php if (isset($_SESSION["user_id"])): ?>
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle me-2"></i>المستخدم مسجل دخول</h5>
                        
                        <?php
                        // الحصول على بيانات المستخدم
                        $user_id = $_SESSION["user_id"];
                        $user_query = "SELECT * FROM users WHERE id = ?";
                        $stmt = $conn->prepare($user_query);
                        $stmt->bind_param("i", $user_id);
                        $stmt->execute();
                        $user_result = $stmt->get_result();
                        
                        if ($user_result->num_rows > 0) {
                            $user = $user_result->fetch_assoc();
                        ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>بيانات المستخدم:</h6>
                                    <ul>
                                        <li><strong>المعرف:</strong> <?php echo $user['id']; ?></li>
                                        <li><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($user['username']); ?></li>
                                        <li><strong>الاسم:</strong> <?php echo htmlspecialchars($user['name']); ?></li>
                                        <li><strong>الصلاحية:</strong> 
                                            <span class="badge bg-<?php echo ($user['role'] == 'admin') ? 'success' : 'warning'; ?>">
                                                <?php echo htmlspecialchars($user['role']); ?>
                                            </span>
                                        </li>
                                        <li><strong>تاريخ الإنشاء:</strong> <?php echo $user['created_at']; ?></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>بيانات الجلسة:</h6>
                                    <ul>
                                        <li><strong>user_id:</strong> <?php echo $_SESSION["user_id"] ?? 'غير محدد'; ?></li>
                                        <li><strong>username:</strong> <?php echo $_SESSION["username"] ?? 'غير محدد'; ?></li>
                                        <li><strong>role:</strong> 
                                            <span class="badge bg-<?php echo ($_SESSION["role"] == 'admin') ? 'success' : 'warning'; ?>">
                                                <?php echo $_SESSION["role"] ?? 'غير محدد'; ?>
                                            </span>
                                        </li>
                                        <li><strong>name:</strong> <?php echo $_SESSION["name"] ?? 'غير محدد'; ?></li>
                                    </ul>
                                </div>
                            </div>
                            
                            <?php if ($user['role'] !== 'admin'): ?>
                                <div class="alert alert-danger mt-3">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>مشكلة الصلاحية!</h6>
                                    <p>المستخدم الحالي ليس له صلاحية <code>admin</code>. الصلاحية الحالية: <code><?php echo $user['role']; ?></code></p>
                                    <p><strong>الحل:</strong> يجب تغيير صلاحية المستخدم إلى <code>admin</code> للوصول لإعادة ضبط المصنع.</p>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-success mt-3">
                                    <h6><i class="fas fa-check-circle me-2"></i>الصلاحية صحيحة!</h6>
                                    <p>المستخدم له صلاحية <code>admin</code> ويجب أن يتمكن من الوصول لإعادة ضبط المصنع.</p>
                                </div>
                            <?php endif; ?>
                            
                        <?php } else { ?>
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>خطأ في البيانات!</h6>
                                <p>لم يتم العثور على بيانات المستخدم في قاعدة البيانات.</p>
                            </div>
                        <?php } ?>
                    </div>
                <?php else: ?>
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-times-circle me-2"></i>المستخدم غير مسجل دخول</h5>
                        <p>يجب تسجيل الدخول أولاً للوصول لإعادة ضبط المصنع.</p>
                        <a href="login.php" class="btn btn-primary">تسجيل الدخول</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <?php if (isset($_SESSION["user_id"])): ?>
            <!-- قائمة جميع المستخدمين -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h3><i class="fas fa-users me-2"></i>جميع المستخدمين في النظام</h3>
                </div>
                <div class="card-body">
                    <?php
                    $all_users_query = "SELECT * FROM users ORDER BY id";
                    $all_users_result = $conn->query($all_users_query);
                    ?>
                    
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>المعرف</th>
                                    <th>اسم المستخدم</th>
                                    <th>الاسم</th>
                                    <th>الصلاحية</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($user = $all_users_result->fetch_assoc()): ?>
                                    <tr class="<?php echo ($user['id'] == $_SESSION['user_id']) ? 'table-warning' : ''; ?>">
                                        <td><?php echo $user['id']; ?></td>
                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                        <td><?php echo htmlspecialchars($user['name']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo ($user['role'] == 'admin') ? 'success' : 'secondary'; ?>">
                                                <?php echo htmlspecialchars($user['role']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo $user['created_at']; ?></td>
                                        <td>
                                            <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                                <span class="badge bg-primary">المستخدم الحالي</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">مستخدم آخر</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- الحلول المقترحة -->
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-tools me-2"></i>الحلول المقترحة</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>الحل الأول: تحديث صلاحية المستخدم الحالي</h5>
                            <p>تغيير صلاحية المستخدم الحالي إلى <code>admin</code></p>
                            
                            <?php if ($_SESSION["role"] !== 'admin'): ?>
                                <form method="POST" action="fix-user-role.php">
                                    <input type="hidden" name="user_id" value="<?php echo $_SESSION['user_id']; ?>">
                                    <input type="hidden" name="new_role" value="admin">
                                    <button type="submit" name="update_role" class="btn btn-success">
                                        <i class="fas fa-user-shield me-2"></i>
                                        ترقية إلى مدير
                                    </button>
                                </form>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    المستخدم الحالي له صلاحية مدير بالفعل
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <h5>الحل الثاني: إزالة قيد الصلاحية</h5>
                            <p>السماح لجميع المستخدمين بالوصول لإعادة ضبط المصنع</p>
                            
                            <form method="POST" action="fix-factory-reset-access.php">
                                <button type="submit" name="remove_admin_restriction" class="btn btn-warning">
                                    <i class="fas fa-unlock me-2"></i>
                                    إزالة قيد المدير
                                </button>
                            </form>
                            
                            <small class="text-muted d-block mt-2">
                                تحذير: هذا سيسمح لجميع المستخدمين بالوصول لإعادة ضبط المصنع
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- روابط مفيدة -->
        <div class="text-center text-white mt-4">
            <div class="row">
                <div class="col-md-3">
                    <a href="index.php" class="btn btn-light btn-lg w-100 mb-2">
                        <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="pages/settings/index.php" class="btn btn-secondary btn-lg w-100 mb-2">
                        <i class="fas fa-cogs me-2"></i>الإعدادات
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="login.php" class="btn btn-info btn-lg w-100 mb-2">
                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="logout.php" class="btn btn-danger btn-lg w-100 mb-2">
                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                    </a>
                </div>
            </div>
            
            <div class="alert alert-warning mt-4">
                <strong>ملاحظة:</strong> احذف هذا الملف بعد حل مشكلة الصلاحية.
            </div>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
