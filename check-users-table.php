<?php
/**
 * فحص جدول المستخدمين
 * Check Users Table Structure
 */

// استدعاء ملفات الإعدادات
require_once "config/db_config.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$table_info = [];
$missing_columns = [];
$table_exists = false;

try {
    // التحقق من وجود الجدول
    $check_table_exists = "SHOW TABLES LIKE 'users'";
    $result = $conn->query($check_table_exists);
    $table_exists = $result->num_rows > 0;
    
    if ($table_exists) {
        // الحصول على هيكل الجدول
        $describe_result = $conn->query("DESCRIBE users");
        $existing_columns = [];
        
        while ($row = $describe_result->fetch_assoc()) {
            $table_info[] = $row;
            $existing_columns[] = $row['Field'];
        }
        
        // الأعمدة المطلوبة
        $required_columns = [
            'id' => 'المعرف الفريد',
            'name' => 'اسم المستخدم',
            'username' => 'اسم المستخدم للدخول',
            'password' => 'كلمة المرور',
            'role' => 'دور المستخدم',
            'email' => 'البريد الإلكتروني',
            'phone' => 'رقم الهاتف',
            'status' => 'حالة المستخدم',
            'created_at' => 'تاريخ الإنشاء',
            'updated_at' => 'تاريخ آخر تحديث'
        ];
        
        // التحقق من الأعمدة المفقودة
        foreach ($required_columns as $column => $description) {
            if (!in_array($column, $existing_columns)) {
                $missing_columns[] = [
                    'column' => $column,
                    'description' => $description
                ];
            }
        }
        
        // عدد المستخدمين
        $count_result = $conn->query("SELECT COUNT(*) as count FROM users");
        $users_count = $count_result->fetch_assoc()['count'];
        
        // عدد المديرين
        $admin_result = $conn->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
        $admin_count = $admin_result->fetch_assoc()['count'];
        
    }
    
} catch (Exception $e) {
    $error_message = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص جدول المستخدمين - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .status-good {
            color: #28a745;
        }
        .status-warning {
            color: #ffc107;
        }
        .status-danger {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-search me-3"></i>
                فحص جدول المستخدمين
            </h1>
            <p class="lead">التحقق من هيكل الجدول والبيانات</p>
        </div>

        <!-- حالة الجدول -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-info-circle me-2"></i>حالة الجدول</h3>
            </div>
            <div class="card-body">
                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        خطأ في الاتصال: <?php echo $error_message; ?>
                    </div>
                <?php elseif (!$table_exists): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-times-circle me-2"></i>
                        جدول المستخدمين غير موجود!
                    </div>
                <?php else: ?>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-success text-white rounded">
                                <i class="fas fa-table fa-2x mb-2"></i>
                                <h5>الجدول موجود</h5>
                                <small>users table</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-primary text-white rounded">
                                <i class="fas fa-columns fa-2x mb-2"></i>
                                <h5><?php echo count($table_info); ?></h5>
                                <small>عدد الأعمدة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-info text-white rounded">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h5><?php echo $users_count ?? 0; ?></h5>
                                <small>عدد المستخدمين</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-warning text-white rounded">
                                <i class="fas fa-user-shield fa-2x mb-2"></i>
                                <h5><?php echo $admin_count ?? 0; ?></h5>
                                <small>عدد المديرين</small>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- الأعمدة المفقودة -->
        <?php if (!empty($missing_columns)): ?>
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h3><i class="fas fa-exclamation-triangle me-2"></i>أعمدة مفقودة</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <strong>تحذير:</strong> يوجد <?php echo count($missing_columns); ?> عمود مفقود من الجدول
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>اسم العمود</th>
                                    <th>الوصف</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($missing_columns as $missing): ?>
                                    <tr>
                                        <td><code><?php echo $missing['column']; ?></code></td>
                                        <td><?php echo $missing['description']; ?></td>
                                        <td><span class="badge bg-danger">مفقود</span></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="fix-users-table.php" class="btn btn-warning btn-lg">
                            <i class="fas fa-wrench me-2"></i>
                            إصلاح الجدول
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- هيكل الجدول -->
        <?php if (!empty($table_info)): ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-table me-2"></i>هيكل الجدول الحالي</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>اسم العمود</th>
                                    <th>النوع</th>
                                    <th>Null</th>
                                    <th>Key</th>
                                    <th>Default</th>
                                    <th>Extra</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $required_columns = ['id', 'name', 'username', 'password', 'role', 'email', 'phone', 'status', 'created_at', 'updated_at'];
                                foreach ($table_info as $column): 
                                    $is_required = in_array($column['Field'], $required_columns);
                                    $status_class = $is_required ? 'status-good' : 'status-warning';
                                    $status_text = $is_required ? 'مطلوب' : 'إضافي';
                                ?>
                                    <tr>
                                        <td><code><?php echo $column['Field']; ?></code></td>
                                        <td><?php echo $column['Type']; ?></td>
                                        <td><?php echo $column['Null']; ?></td>
                                        <td><?php echo $column['Key']; ?></td>
                                        <td><?php echo $column['Default'] ?? 'NULL'; ?></td>
                                        <td><?php echo $column['Extra']; ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $is_required ? 'success' : 'secondary'; ?>">
                                                <?php echo $status_text; ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- التوصيات -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-lightbulb me-2"></i>التوصيات</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>✅ ما يجب فعله:</h5>
                        <ul>
                            <?php if (!empty($missing_columns)): ?>
                                <li>تشغيل أداة إصلاح الجدول</li>
                            <?php endif; ?>
                            <?php if (($admin_count ?? 0) == 0): ?>
                                <li>إنشاء مستخدم admin</li>
                            <?php endif; ?>
                            <li>اختبار إضافة مستخدم جديد</li>
                            <li>التأكد من عمل نظام الأدوار</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>⚠️ تحذيرات:</h5>
                        <ul>
                            <li>عمل نسخة احتياطية قبل التعديل</li>
                            <li>اختبار النظام بعد الإصلاح</li>
                            <li>التأكد من وجود مستخدم admin</li>
                            <li>حذف ملفات الإصلاح بعد الانتهاء</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات سريعة -->
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h3><i class="fas fa-tools me-2"></i>أدوات سريعة</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php if (!empty($missing_columns)): ?>
                        <div class="col-md-3">
                            <a href="fix-users-table.php" class="btn btn-warning w-100 mb-2">
                                <i class="fas fa-wrench me-2"></i>
                                إصلاح الجدول
                            </a>
                        </div>
                    <?php endif; ?>
                    <div class="col-md-3">
                        <a href="pages/settings/users.php" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-users-cog me-2"></i>
                            إدارة المستخدمين
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="pages/settings/backup.php" class="btn btn-success w-100 mb-2">
                            <i class="fas fa-download me-2"></i>
                            نسخة احتياطية
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="index.php" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-home me-2"></i>
                            الصفحة الرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-4">
            <?php if (empty($missing_columns) && $table_exists): ?>
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>الجدول سليم!</h5>
                    <p class="mb-0">جدول المستخدمين يحتوي على جميع الأعمدة المطلوبة</p>
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>يحتاج إصلاح!</h5>
                    <p class="mb-0">يرجى تشغيل أداة الإصلاح لحل المشاكل</p>
                </div>
            <?php endif; ?>
            
            <div class="alert alert-info">
                <strong>ملاحظة:</strong> احذف هذا الملف بعد التأكد من سلامة الجدول:
                <br><code>check-users-table.php</code>
            </div>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحديث الصفحة كل 30 ثانية
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>

</body>
</html>
