<?php
/**
 * ملف الإعدادات للاتصال بقاعدة البيانات
 * Database Configuration File
 */

// معلومات الاتصال بقاعدة البيانات
$db_host = "localhost";     // مضيف قاعدة البيانات
$db_user = "root";          // اسم المستخدم لقاعدة البيانات
$db_pass = "";              // كلمة المرور لقاعدة البيانات
$db_name = "zero";           // اسم قاعدة البيانات

// إنشاء اتصال بقاعدة البيانات
$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
$conn->set_charset("utf8mb4");

// التحقق من الاتصال
if ($conn->connect_error) {
    die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
}

/**
 * دالة للتعامل مع أخطاء الاستعلامات
 * @param string $query نص الاستعلام
 * @param mysqli $connection اتصال قاعدة البيانات
 * @return void
 */
function handleQueryError($query, $connection) {
    error_log("خطأ في الاستعلام: " . $query . " - الخطأ: " . $connection->error);
    die("حدث خطأ في الاستعلام. الرجاء مراجعة سجل الأخطاء للمزيد من المعلومات.");
}

// دالة لتنظيف البيانات المدخلة لمنع SQL Injection
function clean($conn, $data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return mysqli_real_escape_string($conn, $data);
}
?>