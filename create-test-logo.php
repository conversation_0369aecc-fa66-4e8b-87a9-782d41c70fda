<?php
/**
 * إنشاء شعار اختبار
 * Create Test Logo
 */

// إنشاء صورة اختبار بسيطة
$width = 300;
$height = 200;

// إنشاء صورة جديدة
$image = imagecreate($width, $height);

// تحديد الألوان
$background = imagecolorallocate($image, 255, 255, 255); // أبيض
$text_color = imagecolorallocate($image, 0, 102, 204); // أزرق
$border_color = imagecolorallocate($image, 200, 200, 200); // رمادي فاتح

// رسم إطار
imagerectangle($image, 0, 0, $width-1, $height-1, $border_color);
imagerectangle($image, 5, 5, $width-6, $height-6, $border_color);

// إضافة نص
$font_size = 5;
$text1 = "ZERO STORE";
$text2 = "Test Logo";
$text3 = date('Y-m-d H:i');

// حساب موضع النص في المنتصف (تحويل إلى عدد صحيح)
$text1_width = imagefontwidth($font_size) * strlen($text1);
$text1_x = (int)(($width - $text1_width) / 2);
$text1_y = (int)($height / 2 - 30);

$text2_width = imagefontwidth($font_size) * strlen($text2);
$text2_x = (int)(($width - $text2_width) / 2);
$text2_y = (int)($height / 2);

$text3_width = imagefontwidth($font_size) * strlen($text3);
$text3_x = (int)(($width - $text3_width) / 2);
$text3_y = (int)($height / 2 + 30);

// كتابة النص
imagestring($image, $font_size, $text1_x, $text1_y, $text1, $text_color);
imagestring($image, $font_size, $text2_x, $text2_y, $text2, $text_color);
imagestring($image, $font_size, $text3_x, $text3_y, $text3, $text_color);

// إنشاء مجلد الاختبار إذا لم يكن موجوداً
$test_dir = 'test-images/';
if (!is_dir($test_dir)) {
    mkdir($test_dir, 0755, true);
}

// حفظ الصورة
$filename = $test_dir . 'test-logo-' . date('Y-m-d-H-i-s') . '.png';
imagepng($image, $filename);

// تنظيف الذاكرة
imagedestroy($image);

// إنشاء صفحة HTML لعرض النتيجة
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شعار اختبار - نظام Zero</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .logo-preview {
            max-width: 100%;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: white;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-image me-3"></i>
                شعار اختبار
            </h1>
            <p class="lead">تم إنشاء شعار اختبار بنجاح</p>
        </div>

        <!-- عرض الشعار -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h3><i class="fas fa-check-circle me-2"></i>تم إنشاء الشعار بنجاح</h3>
            </div>
            <div class="card-body text-center">
                <div class="logo-preview">
                    <img src="<?php echo $filename; ?>" alt="شعار اختبار" class="img-fluid">
                </div>
                
                <div class="mt-4">
                    <h5>معلومات الملف:</h5>
                    <ul class="list-unstyled">
                        <li><strong>اسم الملف:</strong> <?php echo basename($filename); ?></li>
                        <li><strong>المسار:</strong> <?php echo $filename; ?></li>
                        <li><strong>الحجم:</strong> <?php echo number_format(filesize($filename) / 1024, 2); ?> KB</li>
                        <li><strong>الأبعاد:</strong> <?php echo $width; ?>x<?php echo $height; ?> بكسل</li>
                        <li><strong>النوع:</strong> PNG</li>
                    </ul>
                </div>
                
                <div class="mt-4">
                    <a href="<?php echo $filename; ?>" download class="btn btn-primary me-2">
                        <i class="fas fa-download me-2"></i>
                        تحميل الشعار
                    </a>
                    <a href="pages/settings/logo.php" class="btn btn-success me-2">
                        <i class="fas fa-upload me-2"></i>
                        رفع هذا الشعار
                    </a>
                    <a href="test-logo-upload.php" class="btn btn-info">
                        <i class="fas fa-bug me-2"></i>
                        اختبار الرفع
                    </a>
                </div>
            </div>
        </div>

        <!-- تعليمات الاستخدام -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-info-circle me-2"></i>كيفية استخدام هذا الشعار</h3>
            </div>
            <div class="card-body">
                <ol>
                    <li><strong>تحميل الشعار:</strong> انقر على زر "تحميل الشعار" أعلاه</li>
                    <li><strong>الانتقال لإدارة الشعار:</strong> انقر على "رفع هذا الشعار" أو اذهب يدوياً إلى الإعدادات > شعار المحل</li>
                    <li><strong>رفع الشعار:</strong> اختر الملف المحمل ورفعه</li>
                    <li><strong>اختبار الفاتورة:</strong> اذهب إلى أي فاتورة وتأكد من ظهور الشعار</li>
                </ol>
                
                <div class="alert alert-success mt-3">
                    <h6><i class="fas fa-lightbulb me-2"></i>نصائح:</h6>
                    <ul class="mb-0">
                        <li>هذا الشعار مصمم خصيصاً ليعمل مع النظام</li>
                        <li>الحجم والنوع مناسبان للرفع</li>
                        <li>إذا نجح رفع هذا الشعار، فالنظام يعمل بشكل صحيح</li>
                        <li>يمكنك استخدام هذا الشعار كاختبار أولي</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- إنشاء شعار جديد -->
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-plus-circle me-2"></i>إنشاء شعار جديد</h3>
            </div>
            <div class="card-body">
                <p>يمكنك إنشاء شعار اختبار جديد بنقرة واحدة:</p>
                
                <form method="POST">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="logo_width" class="form-label">العرض (بكسل):</label>
                            <input type="number" class="form-control" id="logo_width" name="logo_width" value="300" min="100" max="800">
                        </div>
                        <div class="col-md-4">
                            <label for="logo_height" class="form-label">الارتفاع (بكسل):</label>
                            <input type="number" class="form-control" id="logo_height" name="logo_height" value="200" min="100" max="600">
                        </div>
                        <div class="col-md-4">
                            <label for="logo_text" class="form-label">النص:</label>
                            <input type="text" class="form-control" id="logo_text" name="logo_text" value="ZERO STORE">
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <button type="submit" name="create_new" class="btn btn-warning">
                            <i class="fas fa-magic me-2"></i>
                            إنشاء شعار جديد
                        </button>
                    </div>
                </form>
                
                <?php
                // معالجة إنشاء شعار جديد
                if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_new'])) {
                    $new_width = intval($_POST['logo_width']) ?: 300;
                    $new_height = intval($_POST['logo_height']) ?: 200;
                    $new_text = $_POST['logo_text'] ?: 'ZERO STORE';
                    
                    // إنشاء صورة جديدة
                    $new_image = imagecreate($new_width, $new_height);
                    $new_bg = imagecolorallocate($new_image, 255, 255, 255);
                    $new_text_color = imagecolorallocate($new_image, 0, 102, 204);
                    $new_border = imagecolorallocate($new_image, 200, 200, 200);
                    
                    // رسم إطار
                    imagerectangle($new_image, 0, 0, $new_width-1, $new_height-1, $new_border);
                    
                    // إضافة النص (تحويل إلى عدد صحيح)
                    $new_text_width = imagefontwidth(5) * strlen($new_text);
                    $new_text_x = (int)(($new_width - $new_text_width) / 2);
                    $new_text_y = (int)($new_height / 2 - 10);

                    imagestring($new_image, 5, $new_text_x, $new_text_y, $new_text, $new_text_color);
                    
                    // حفظ الصورة الجديدة
                    $new_filename = $test_dir . 'custom-logo-' . date('Y-m-d-H-i-s') . '.png';
                    imagepng($new_image, $new_filename);
                    imagedestroy($new_image);
                    
                    echo '<div class="alert alert-success mt-3">';
                    echo '<h6>تم إنشاء شعار جديد!</h6>';
                    echo '<img src="' . $new_filename . '" alt="شعار جديد" class="img-fluid" style="max-width: 200px; border: 1px solid #ddd; border-radius: 5px;">';
                    echo '<br><a href="' . $new_filename . '" download class="btn btn-sm btn-primary mt-2">تحميل الشعار الجديد</a>';
                    echo '</div>';
                }
                ?>
            </div>
        </div>

        <!-- روابط مفيدة -->
        <div class="text-center text-white mt-4">
            <a href="pages/settings/logo.php" class="btn btn-light btn-lg me-3">
                <i class="fas fa-image me-2"></i>
                إدارة الشعار
            </a>
            <a href="test-logo-upload.php" class="btn btn-outline-light btn-lg me-3">
                <i class="fas fa-bug me-2"></i>
                اختبار الرفع
            </a>
            <a href="pages/settings/index.php" class="btn btn-outline-light btn-lg">
                <i class="fas fa-cogs me-2"></i>
                الإعدادات
            </a>
            
            <div class="alert alert-info mt-4">
                <strong>ملاحظة:</strong> احذف هذا الملف ومجلد test-images بعد الانتهاء من الاختبار:
                <br><code>create-test-logo.php</code> و <code>test-images/</code>
            </div>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
