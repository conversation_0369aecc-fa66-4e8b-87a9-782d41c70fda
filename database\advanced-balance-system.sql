-- تحديث نظام إدارة الرصيد المتقدم
-- Advanced Balance Management System Update

-- تحديث جدول العملاء لإضافة نظام الرصيد المتقدم (إضافة الأعمدة واحداً تلو الآخر)
ALTER TABLE `customers` ADD COLUMN IF NOT EXISTS `wallet_balance` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'الرصيد المحفوظ (الفكة الزائدة)';
ALTER TABLE `customers` ADD COLUMN IF NOT EXISTS `debt_balance` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'رصيد الديون (المبالغ المستحقة)';
ALTER TABLE `customers` ADD COLUMN IF NOT EXISTS `credit_limit` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'حد الائتمان المسموح';
ALTER TABLE `customers` ADD COLUMN IF NOT EXISTS `last_transaction_date` DATETIME NULL COMMENT 'تاريخ آخر معاملة';

-- تحديث جدول الموردين لإضافة نظام الرصيد المتقدم (إضافة الأعمدة واحداً تلو الآخر)
ALTER TABLE `suppliers` ADD COLUMN IF NOT EXISTS `wallet_balance` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'الرصيد المحفوظ (المبالغ الزائدة)';
ALTER TABLE `suppliers` ADD COLUMN IF NOT EXISTS `debt_balance` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'رصيد الديون (المبالغ المستحقة لنا)';
ALTER TABLE `suppliers` ADD COLUMN IF NOT EXISTS `credit_limit` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'حد الائتمان المسموح';
ALTER TABLE `suppliers` ADD COLUMN IF NOT EXISTS `last_transaction_date` DATETIME NULL COMMENT 'تاريخ آخر معاملة';

-- إنشاء جدول تتبع معاملات الرصيد
CREATE TABLE IF NOT EXISTS `balance_transactions` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `entity_type` ENUM('customer', 'supplier') NOT NULL COMMENT 'نوع الكيان',
  `entity_id` INT(11) NOT NULL COMMENT 'معرف العميل أو المورد',
  `transaction_type` ENUM('wallet_add', 'wallet_use', 'debt_add', 'debt_pay', 'balance_transfer') NOT NULL COMMENT 'نوع المعاملة',
  `amount` DECIMAL(10,2) NOT NULL COMMENT 'المبلغ',
  `wallet_balance_before` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'الرصيد المحفوظ قبل المعاملة',
  `wallet_balance_after` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'الرصيد المحفوظ بعد المعاملة',
  `debt_balance_before` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'رصيد الديون قبل المعاملة',
  `debt_balance_after` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'رصيد الديون بعد المعاملة',
  `reference_type` ENUM('sale', 'purchase', 'payment', 'manual') NOT NULL COMMENT 'نوع المرجع',
  `reference_id` INT(11) NULL COMMENT 'معرف المرجع',
  `description` TEXT NULL COMMENT 'وصف المعاملة',
  `user_id` INT(11) NOT NULL COMMENT 'المستخدم الذي قام بالمعاملة',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `entity_idx` (`entity_type`, `entity_id`),
  INDEX `reference_idx` (`reference_type`, `reference_id`),
  INDEX `date_idx` (`created_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- تحديث جدول المبيعات لإضافة تفاصيل الدفع المتقدم (إضافة الأعمدة واحداً تلو الآخر)
ALTER TABLE `sales` ADD COLUMN IF NOT EXISTS `wallet_used` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'المبلغ المستخدم من الرصيد المحفوظ';
ALTER TABLE `sales` ADD COLUMN IF NOT EXISTS `cash_paid` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'المبلغ المدفوع نقداً';
ALTER TABLE `sales` ADD COLUMN IF NOT EXISTS `change_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'مبلغ الفكة';
ALTER TABLE `sales` ADD COLUMN IF NOT EXISTS `wallet_added` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'المبلغ المضاف للرصيد المحفوظ';
ALTER TABLE `sales` ADD COLUMN IF NOT EXISTS `debt_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'مبلغ الدين المسجل';

-- تحديث جدول المشتريات لإضافة تفاصيل الدفع المتقدم (إضافة الأعمدة واحداً تلو الآخر)
ALTER TABLE `purchases` ADD COLUMN IF NOT EXISTS `wallet_used` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'المبلغ المستخدم من الرصيد المحفوظ';
ALTER TABLE `purchases` ADD COLUMN IF NOT EXISTS `cash_paid` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'المبلغ المدفوع نقداً';
ALTER TABLE `purchases` ADD COLUMN IF NOT EXISTS `change_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'مبلغ الفكة';
ALTER TABLE `purchases` ADD COLUMN IF NOT EXISTS `wallet_added` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'المبلغ المضاف للرصيد المحفوظ';
ALTER TABLE `purchases` ADD COLUMN IF NOT EXISTS `debt_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00' COMMENT 'مبلغ الدين المسجل';

-- إنشاء جدول إعدادات نظام الرصيد
CREATE TABLE IF NOT EXISTS `balance_settings` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `setting_key` VARCHAR(50) NOT NULL,
  `setting_value` TEXT NOT NULL,
  `description` TEXT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج إعدادات افتراضية لنظام الرصيد
INSERT INTO `balance_settings` (`setting_key`, `setting_value`, `description`) VALUES
('min_wallet_usage', '10.00', 'الحد الأدنى للرصيد المحفوظ لعرض خيار الاستخدام'),
('auto_debt_deduction', '1', 'خصم تلقائي من الرصيد المحفوظ عند تسجيل دين'),
('wallet_notification_threshold', '50.00', 'حد التنبيه عند وصول الرصيد المحفوظ لمبلغ معين'),
('default_credit_limit', '1000.00', 'حد الائتمان الافتراضي للعملاء الجدد'),
('allow_negative_balance', '1', 'السماح بالرصيد السالب (الديون)'),
('max_debt_days', '30', 'عدد الأيام القصوى للديون قبل التنبيه');

-- إنشاء فيو لعرض ملخص أرصدة العملاء
CREATE OR REPLACE VIEW `customer_balance_summary` AS
SELECT 
    c.id,
    c.name,
    c.phone,
    c.wallet_balance,
    c.debt_balance,
    c.credit_limit,
    (c.wallet_balance - c.debt_balance) AS net_balance,
    CASE 
        WHEN (c.wallet_balance - c.debt_balance) > 0 THEN 'دائن'
        WHEN (c.wallet_balance - c.debt_balance) < 0 THEN 'مدين'
        ELSE 'متعادل'
    END AS balance_status,
    c.last_transaction_date,
    DATEDIFF(NOW(), c.last_transaction_date) AS days_since_last_transaction
FROM customers c;

-- إنشاء فيو لعرض ملخص أرصدة الموردين
CREATE OR REPLACE VIEW `supplier_balance_summary` AS
SELECT 
    s.id,
    s.name,
    s.phone,
    s.wallet_balance,
    s.debt_balance,
    s.credit_limit,
    (s.wallet_balance - s.debt_balance) AS net_balance,
    CASE 
        WHEN (s.wallet_balance - s.debt_balance) > 0 THEN 'دائن'
        WHEN (s.wallet_balance - s.debt_balance) < 0 THEN 'مدين'
        ELSE 'متعادل'
    END AS balance_status,
    s.last_transaction_date,
    DATEDIFF(NOW(), s.last_transaction_date) AS days_since_last_transaction
FROM suppliers s;

-- إنشاء مؤشرات لتحسين الأداء
CREATE INDEX idx_customers_wallet_balance ON customers(wallet_balance);
CREATE INDEX idx_customers_debt_balance ON customers(debt_balance);
CREATE INDEX idx_customers_last_transaction ON customers(last_transaction_date);

CREATE INDEX idx_suppliers_wallet_balance ON suppliers(wallet_balance);
CREATE INDEX idx_suppliers_debt_balance ON suppliers(debt_balance);
CREATE INDEX idx_suppliers_last_transaction ON suppliers(last_transaction_date);

-- إنشاء إجراءات مخزنة لإدارة الرصيد

-- إجراء لإضافة رصيد محفوظ
DELIMITER //
CREATE PROCEDURE AddWalletBalance(
    IN p_entity_type ENUM('customer', 'supplier'),
    IN p_entity_id INT,
    IN p_amount DECIMAL(10,2),
    IN p_reference_type ENUM('sale', 'purchase', 'payment', 'manual'),
    IN p_reference_id INT,
    IN p_description TEXT,
    IN p_user_id INT
)
BEGIN
    DECLARE v_current_wallet DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_current_debt DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_new_wallet DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_new_debt DECIMAL(10,2) DEFAULT 0.00;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- الحصول على الأرصدة الحالية
    IF p_entity_type = 'customer' THEN
        SELECT wallet_balance, debt_balance INTO v_current_wallet, v_current_debt
        FROM customers WHERE id = p_entity_id FOR UPDATE;
    ELSE
        SELECT wallet_balance, debt_balance INTO v_current_wallet, v_current_debt
        FROM suppliers WHERE id = p_entity_id FOR UPDATE;
    END IF;
    
    -- حساب الأرصدة الجديدة
    SET v_new_wallet = v_current_wallet + p_amount;
    SET v_new_debt = v_current_debt;
    
    -- تحديث الرصيد
    IF p_entity_type = 'customer' THEN
        UPDATE customers 
        SET wallet_balance = v_new_wallet, last_transaction_date = NOW()
        WHERE id = p_entity_id;
    ELSE
        UPDATE suppliers 
        SET wallet_balance = v_new_wallet, last_transaction_date = NOW()
        WHERE id = p_entity_id;
    END IF;
    
    -- تسجيل المعاملة
    INSERT INTO balance_transactions (
        entity_type, entity_id, transaction_type, amount,
        wallet_balance_before, wallet_balance_after,
        debt_balance_before, debt_balance_after,
        reference_type, reference_id, description, user_id
    ) VALUES (
        p_entity_type, p_entity_id, 'wallet_add', p_amount,
        v_current_wallet, v_new_wallet,
        v_current_debt, v_new_debt,
        p_reference_type, p_reference_id, p_description, p_user_id
    );
    
    COMMIT;
END //
DELIMITER ;

-- إجراء لاستخدام الرصيد المحفوظ
DELIMITER //
CREATE PROCEDURE UseWalletBalance(
    IN p_entity_type ENUM('customer', 'supplier'),
    IN p_entity_id INT,
    IN p_amount DECIMAL(10,2),
    IN p_reference_type ENUM('sale', 'purchase', 'payment', 'manual'),
    IN p_reference_id INT,
    IN p_description TEXT,
    IN p_user_id INT,
    OUT p_used_amount DECIMAL(10,2)
)
BEGIN
    DECLARE v_current_wallet DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_current_debt DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_new_wallet DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_new_debt DECIMAL(10,2) DEFAULT 0.00;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- الحصول على الأرصدة الحالية
    IF p_entity_type = 'customer' THEN
        SELECT wallet_balance, debt_balance INTO v_current_wallet, v_current_debt
        FROM customers WHERE id = p_entity_id FOR UPDATE;
    ELSE
        SELECT wallet_balance, debt_balance INTO v_current_wallet, v_current_debt
        FROM suppliers WHERE id = p_entity_id FOR UPDATE;
    END IF;
    
    -- تحديد المبلغ المستخدم (لا يمكن أن يتجاوز الرصيد المتاح)
    SET p_used_amount = LEAST(p_amount, v_current_wallet);
    
    -- حساب الأرصدة الجديدة
    SET v_new_wallet = v_current_wallet - p_used_amount;
    SET v_new_debt = v_current_debt;
    
    -- تحديث الرصيد
    IF p_entity_type = 'customer' THEN
        UPDATE customers 
        SET wallet_balance = v_new_wallet, last_transaction_date = NOW()
        WHERE id = p_entity_id;
    ELSE
        UPDATE suppliers 
        SET wallet_balance = v_new_wallet, last_transaction_date = NOW()
        WHERE id = p_entity_id;
    END IF;
    
    -- تسجيل المعاملة
    INSERT INTO balance_transactions (
        entity_type, entity_id, transaction_type, amount,
        wallet_balance_before, wallet_balance_after,
        debt_balance_before, debt_balance_after,
        reference_type, reference_id, description, user_id
    ) VALUES (
        p_entity_type, p_entity_id, 'wallet_use', p_used_amount,
        v_current_wallet, v_new_wallet,
        v_current_debt, v_new_debt,
        p_reference_type, p_reference_id, p_description, p_user_id
    );
    
    COMMIT;
END //
DELIMITER ;

-- إجراء لإضافة دين
DELIMITER //
CREATE PROCEDURE AddDebtBalance(
    IN p_entity_type ENUM('customer', 'supplier'),
    IN p_entity_id INT,
    IN p_amount DECIMAL(10,2),
    IN p_reference_type ENUM('sale', 'purchase', 'payment', 'manual'),
    IN p_reference_id INT,
    IN p_description TEXT,
    IN p_user_id INT,
    IN p_auto_deduct BOOLEAN
)
BEGIN
    DECLARE v_current_wallet DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_current_debt DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_new_wallet DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_new_debt DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_deducted_amount DECIMAL(10,2) DEFAULT 0.00;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- الحصول على الأرصدة الحالية
    IF p_entity_type = 'customer' THEN
        SELECT wallet_balance, debt_balance INTO v_current_wallet, v_current_debt
        FROM customers WHERE id = p_entity_id FOR UPDATE;
    ELSE
        SELECT wallet_balance, debt_balance INTO v_current_wallet, v_current_debt
        FROM suppliers WHERE id = p_entity_id FOR UPDATE;
    END IF;
    
    -- الخصم التلقائي من الرصيد المحفوظ إذا كان مفعلاً
    IF p_auto_deduct AND v_current_wallet > 0 THEN
        SET v_deducted_amount = LEAST(p_amount, v_current_wallet);
        SET v_new_wallet = v_current_wallet - v_deducted_amount;
        SET v_new_debt = v_current_debt + (p_amount - v_deducted_amount);
    ELSE
        SET v_new_wallet = v_current_wallet;
        SET v_new_debt = v_current_debt + p_amount;
    END IF;
    
    -- تحديث الأرصدة
    IF p_entity_type = 'customer' THEN
        UPDATE customers 
        SET wallet_balance = v_new_wallet, debt_balance = v_new_debt, last_transaction_date = NOW()
        WHERE id = p_entity_id;
    ELSE
        UPDATE suppliers 
        SET wallet_balance = v_new_wallet, debt_balance = v_new_debt, last_transaction_date = NOW()
        WHERE id = p_entity_id;
    END IF;
    
    -- تسجيل المعاملة
    INSERT INTO balance_transactions (
        entity_type, entity_id, transaction_type, amount,
        wallet_balance_before, wallet_balance_after,
        debt_balance_before, debt_balance_after,
        reference_type, reference_id, description, user_id
    ) VALUES (
        p_entity_type, p_entity_id, 'debt_add', p_amount,
        v_current_wallet, v_new_wallet,
        v_current_debt, v_new_debt,
        p_reference_type, p_reference_id, p_description, p_user_id
    );
    
    COMMIT;
END //
DELIMITER ;

-- تحديث البيانات الموجودة (نقل الرصيد القديم إلى النظام الجديد)
UPDATE customers SET 
    wallet_balance = CASE WHEN balance > 0 THEN balance ELSE 0 END,
    debt_balance = CASE WHEN balance < 0 THEN ABS(balance) ELSE 0 END
WHERE balance != 0;

UPDATE suppliers SET 
    wallet_balance = CASE WHEN balance > 0 THEN balance ELSE 0 END,
    debt_balance = CASE WHEN balance < 0 THEN ABS(balance) ELSE 0 END
WHERE balance != 0;
