<?php
/**
 * إصلاح الوصول لإعادة ضبط المصنع
 * Fix Factory Reset Access
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "config/db_config.php";
require_once "includes/functions.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$message = '';
$message_type = '';

// معالجة إزالة قيد المدير
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['remove_admin_restriction'])) {
    try {
        // قراءة ملف إعادة ضبط المصنع
        $factory_reset_file = 'pages/settings/factory-reset.php';
        
        if (file_exists($factory_reset_file)) {
            $content = file_get_contents($factory_reset_file);
            
            // إزالة التحقق من صلاحية المدير
            $old_code = '// التحقق من صلاحية المدير فقط
if ($_SESSION["role"] !== \'admin\') {
    $_SESSION[\'error_message\'] = "غير مسموح لك بالوصول لهذه الصفحة";
    header("Location: ../../index.php");
    exit();
}';

            $new_code = '// تم إزالة قيد صلاحية المدير - يمكن لجميع المستخدمين الوصول
// if ($_SESSION["role"] !== \'admin\') {
//     $_SESSION[\'error_message\'] = "غير مسموح لك بالوصول لهذه الصفحة";
//     header("Location: ../../index.php");
//     exit();
// }';

            $updated_content = str_replace($old_code, $new_code, $content);
            
            if ($updated_content !== $content) {
                file_put_contents($factory_reset_file, $updated_content);
                $message = "تم إزالة قيد صلاحية المدير بنجاح. يمكن لجميع المستخدمين الآن الوصول لإعادة ضبط المصنع.";
                $message_type = 'success';
            } else {
                $message = "لم يتم العثور على الكود المطلوب تعديله أو تم تعديله مسبقاً.";
                $message_type = 'warning';
            }
        } else {
            $message = "ملف إعادة ضبط المصنع غير موجود.";
            $message_type = 'danger';
        }
        
    } catch (Exception $e) {
        $message = "خطأ في إزالة قيد الصلاحية: " . $e->getMessage();
        $message_type = 'danger';
    }
}

// معالجة إعادة قيد المدير
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['restore_admin_restriction'])) {
    try {
        // قراءة ملف إعادة ضبط المصنع
        $factory_reset_file = 'pages/settings/factory-reset.php';
        
        if (file_exists($factory_reset_file)) {
            $content = file_get_contents($factory_reset_file);
            
            // إعادة التحقق من صلاحية المدير
            $old_code = '// تم إزالة قيد صلاحية المدير - يمكن لجميع المستخدمين الوصول
// if ($_SESSION["role"] !== \'admin\') {
//     $_SESSION[\'error_message\'] = "غير مسموح لك بالوصول لهذه الصفحة";
//     header("Location: ../../index.php");
//     exit();
// }';

            $new_code = '// التحقق من صلاحية المدير فقط
if ($_SESSION["role"] !== \'admin\') {
    $_SESSION[\'error_message\'] = "غير مسموح لك بالوصول لهذه الصفحة";
    header("Location: ../../index.php");
    exit();
}';

            $updated_content = str_replace($old_code, $new_code, $content);
            
            if ($updated_content !== $content) {
                file_put_contents($factory_reset_file, $updated_content);
                $message = "تم إعادة قيد صلاحية المدير بنجاح. فقط المديرون يمكنهم الآن الوصول لإعادة ضبط المصنع.";
                $message_type = 'success';
            } else {
                $message = "لم يتم العثور على الكود المطلوب تعديله أو تم تعديله مسبقاً.";
                $message_type = 'warning';
            }
        } else {
            $message = "ملف إعادة ضبط المصنع غير موجود.";
            $message_type = 'danger';
        }
        
    } catch (Exception $e) {
        $message = "خطأ في إعادة قيد الصلاحية: " . $e->getMessage();
        $message_type = 'danger';
    }
}

// فحص الحالة الحالية
$current_restriction = 'unknown';
$factory_reset_file = 'pages/settings/factory-reset.php';

if (file_exists($factory_reset_file)) {
    $content = file_get_contents($factory_reset_file);
    
    if (strpos($content, '// تم إزالة قيد صلاحية المدير') !== false) {
        $current_restriction = 'removed';
    } elseif (strpos($content, 'if ($_SESSION["role"] !== \'admin\')') !== false) {
        $current_restriction = 'active';
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح الوصول لإعادة ضبط المصنع - نظام Zero</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-unlock-alt me-3"></i>
                إصلاح الوصول لإعادة ضبط المصنع
            </h1>
            <p class="lead">إزالة أو إعادة قيد صلاحية المدير</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?php echo ($message_type == 'success') ? 'check-circle' : 'exclamation-circle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- الحالة الحالية -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-info-circle me-2"></i>الحالة الحالية</h3>
            </div>
            <div class="card-body">
                <?php if ($current_restriction == 'active'): ?>
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-lock me-2"></i>قيد المدير مفعل</h5>
                        <p>حالياً، فقط المستخدمون الذين لديهم صلاحية <code>admin</code> يمكنهم الوصول لإعادة ضبط المصنع.</p>
                    </div>
                <?php elseif ($current_restriction == 'removed'): ?>
                    <div class="alert alert-success">
                        <h5><i class="fas fa-unlock me-2"></i>قيد المدير مُزال</h5>
                        <p>حالياً، جميع المستخدمين المسجلين يمكنهم الوصول لإعادة ضبط المصنع.</p>
                    </div>
                <?php else: ?>
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-question-circle me-2"></i>حالة غير معروفة</h5>
                        <p>لا يمكن تحديد الحالة الحالية لقيد الصلاحية.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- الخيارات المتاحة -->
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-tools me-2"></i>خيارات الإصلاح</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>الخيار الأول: إزالة قيد المدير</h5>
                        <p>السماح لجميع المستخدمين المسجلين بالوصول لإعادة ضبط المصنع.</p>
                        
                        <div class="alert alert-danger">
                            <strong>تحذير:</strong> هذا سيسمح لأي مستخدم مسجل بمسح جميع البيانات!
                        </div>
                        
                        <?php if ($current_restriction != 'removed'): ?>
                            <form method="POST">
                                <button type="submit" name="remove_admin_restriction" class="btn btn-warning" 
                                        onclick="return confirm('هل أنت متأكد من إزالة قيد المدير؟\n\nهذا سيسمح لجميع المستخدمين بالوصول لإعادة ضبط المصنع!')">
                                    <i class="fas fa-unlock me-2"></i>
                                    إزالة قيد المدير
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                قيد المدير مُزال بالفعل
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-6">
                        <h5>الخيار الثاني: إعادة قيد المدير</h5>
                        <p>السماح فقط للمديرين بالوصول لإعادة ضبط المصنع (الإعداد الافتراضي).</p>
                        
                        <div class="alert alert-success">
                            <strong>موصى به:</strong> هذا الخيار أكثر أماناً ويحمي النظام من الحذف العرضي.
                        </div>
                        
                        <?php if ($current_restriction != 'active'): ?>
                            <form method="POST">
                                <button type="submit" name="restore_admin_restriction" class="btn btn-success">
                                    <i class="fas fa-lock me-2"></i>
                                    إعادة قيد المدير
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                قيد المدير مفعل بالفعل
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- الحلول البديلة -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h3><i class="fas fa-lightbulb me-2"></i>الحلول البديلة</h3>
            </div>
            <div class="card-body">
                <h5>بدلاً من إزالة قيد المدير، يمكنك:</h5>
                <ol>
                    <li><strong>ترقية المستخدم الحالي إلى مدير:</strong>
                        <br><a href="fix-user-role.php" class="btn btn-primary btn-sm mt-1">
                            <i class="fas fa-user-shield me-1"></i>إصلاح صلاحية المستخدم
                        </a>
                    </li>
                    <li><strong>تسجيل الدخول بحساب مدير آخر</strong></li>
                    <li><strong>إنشاء مستخدم مدير جديد من قاعدة البيانات</strong></li>
                </ol>
                
                <div class="alert alert-info mt-3">
                    <h6><i class="fas fa-info-circle me-2"></i>نصيحة:</h6>
                    <p class="mb-0">الحل الأفضل هو ترقية المستخدم الحالي إلى مدير بدلاً من إزالة قيد الأمان.</p>
                </div>
            </div>
        </div>

        <!-- معلومات تقنية -->
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h3><i class="fas fa-code me-2"></i>معلومات تقنية</h3>
            </div>
            <div class="card-body">
                <h5>ما يحدث عند إزالة قيد المدير:</h5>
                <pre class="bg-light p-3 rounded"><code>// الكود الأصلي (مع القيد):
if ($_SESSION["role"] !== 'admin') {
    $_SESSION['error_message'] = "غير مسموح لك بالوصول لهذه الصفحة";
    header("Location: ../../index.php");
    exit();
}

// الكود بعد الإزالة (بدون قيد):
// تم إزالة قيد صلاحية المدير - يمكن لجميع المستخدمين الوصول
// if ($_SESSION["role"] !== 'admin') {
//     $_SESSION['error_message'] = "غير مسموح لك بالوصول لهذه الصفحة";
//     header("Location: ../../index.php");
//     exit();
// }</code></pre>
            </div>
        </div>

        <!-- روابط مفيدة -->
        <div class="text-center text-white mt-4">
            <h3>🔧 اختر الحل المناسب 🔧</h3>
            <p>يمكنك إما إزالة قيد المدير أو ترقية المستخدم الحالي</p>
            
            <div class="row mt-4">
                <div class="col-md-3">
                    <a href="fix-user-role.php" class="btn btn-light btn-lg w-100 mb-2">
                        <i class="fas fa-user-shield me-2"></i>ترقية المستخدم
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="check-user-role.php" class="btn btn-info btn-lg w-100 mb-2">
                        <i class="fas fa-user-check me-2"></i>فحص الصلاحية
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="pages/settings/factory-reset.php" class="btn btn-danger btn-lg w-100 mb-2">
                        <i class="fas fa-exclamation-triangle me-2"></i>إعادة ضبط المصنع
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="index.php" class="btn btn-secondary btn-lg w-100 mb-2">
                        <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                    </a>
                </div>
            </div>
            
            <div class="alert alert-warning mt-4">
                <strong>ملاحظة:</strong> احذف ملفات الإصلاح بعد حل المشكلة.
            </div>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
