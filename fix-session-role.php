<?php
/**
 * إصلاح مشكلة دور المستخدم في الجلسة
 * Fix User Role Session Issue
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "config/db_config.php";
require_once "includes/functions.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$message = '';
$message_type = '';

// التحقق من تسجيل الدخول
if (!isset($_SESSION["user_id"])) {
    header("Location: login.php");
    exit();
}

// الحصول على بيانات المستخدم من قاعدة البيانات
$user_id = $_SESSION["user_id"];
$user_query = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($user_query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$user_result = $stmt->get_result();

if ($user_result->num_rows > 0) {
    $user_data = $user_result->fetch_assoc();
    
    // تحديث الجلسة بالبيانات الصحيحة
    $_SESSION['user_name'] = $user_data['name'];
    $_SESSION['username'] = $user_data['username'];
    $_SESSION['role'] = $user_data['role'];
    
    $message = "تم إصلاح بيانات الجلسة بنجاح. الدور الحالي: " . $user_data['role'];
    $message_type = 'success';
} else {
    $message = "خطأ: لم يتم العثور على بيانات المستخدم";
    $message_type = 'danger';
}

// معالجة إعادة التوجيه
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['redirect_to'])) {
    $redirect_to = $_POST['redirect_to'];
    header("Location: $redirect_to");
    exit();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح الجلسة - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-user-cog me-3"></i>
                إصلاح بيانات الجلسة
            </h1>
            <p class="lead">تم إصلاح مشكلة دور المستخدم في الجلسة</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?php echo ($message_type == 'success') ? 'check-circle' : 'exclamation-circle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- معلومات الجلسة الحالية -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-info-circle me-2"></i>معلومات الجلسة الحالية</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>بيانات المستخدم:</h5>
                        <ul class="list-group">
                            <li class="list-group-item">
                                <strong>المعرف:</strong> <?php echo $_SESSION['user_id'] ?? 'غير محدد'; ?>
                            </li>
                            <li class="list-group-item">
                                <strong>الاسم:</strong> <?php echo $_SESSION['user_name'] ?? 'غير محدد'; ?>
                            </li>
                            <li class="list-group-item">
                                <strong>اسم المستخدم:</strong> <?php echo $_SESSION['username'] ?? 'غير محدد'; ?>
                            </li>
                            <li class="list-group-item">
                                <strong>الدور:</strong> 
                                <span class="badge bg-<?php echo ($_SESSION['role'] ?? '') === 'admin' ? 'success' : (($_SESSION['role'] ?? '') === 'manager' ? 'warning' : 'secondary'); ?>">
                                    <?php 
                                    $roles = [
                                        'admin' => 'مدير',
                                        'manager' => 'مدير فرع',
                                        'cashier' => 'كاشير'
                                    ];
                                    echo $roles[$_SESSION['role'] ?? ''] ?? ($_SESSION['role'] ?? 'غير محدد');
                                    ?>
                                </span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>حالة الإصلاح:</h5>
                        <?php if ($message_type === 'success'): ?>
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle me-2"></i>تم الإصلاح بنجاح!</h6>
                                <p class="mb-0">تم تحديث بيانات الجلسة من قاعدة البيانات. يمكنك الآن الوصول لجميع الصفحات.</p>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>فشل الإصلاح!</h6>
                                <p class="mb-0">لم يتم العثور على بيانات المستخدم في قاعدة البيانات. يرجى تسجيل الدخول مرة أخرى.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- خيارات التنقل -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-compass me-2"></i>إلى أين تريد الذهاب؟</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <form method="POST">
                            <input type="hidden" name="redirect_to" value="index.php">
                            <button type="submit" class="btn btn-primary w-100 mb-2">
                                <i class="fas fa-home me-2"></i>
                                الصفحة الرئيسية
                            </button>
                        </form>
                    </div>
                    <div class="col-md-3">
                        <form method="POST">
                            <input type="hidden" name="redirect_to" value="profile.php">
                            <button type="submit" class="btn btn-info w-100 mb-2">
                                <i class="fas fa-user-cog me-2"></i>
                                الملف الشخصي
                            </button>
                        </form>
                    </div>
                    <?php if (($_SESSION['role'] ?? '') === 'admin'): ?>
                        <div class="col-md-3">
                            <form method="POST">
                                <input type="hidden" name="redirect_to" value="pages/settings/index.php">
                                <button type="submit" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-cogs me-2"></i>
                                    الإعدادات
                                </button>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="POST">
                                <input type="hidden" name="redirect_to" value="pages/settings/backup.php">
                                <button type="submit" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-download me-2"></i>
                                    النسخ الاحتياطي
                                </button>
                            </form>
                        </div>
                    <?php else: ?>
                        <div class="col-md-3">
                            <form method="POST">
                                <input type="hidden" name="redirect_to" value="pages/sales/add.php">
                                <button type="submit" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-plus me-2"></i>
                                    فاتورة مبيعة
                                </button>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="POST">
                                <input type="hidden" name="redirect_to" value="pages/products/index.php">
                                <button type="submit" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-box me-2"></i>
                                    المنتجات
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h3><i class="fas fa-info me-2"></i>ما الذي حدث؟</h3>
            </div>
            <div class="card-body">
                <h5>المشكلة:</h5>
                <p>كانت بيانات الدور (role) مفقودة من الجلسة، مما تسبب في ظهور رسالة "غير مسموح لك بالوصول لهذه الصفحة".</p>
                
                <h5>الحل:</h5>
                <p>تم استرجاع بيانات المستخدم من قاعدة البيانات وتحديث الجلسة بالمعلومات الصحيحة.</p>
                
                <h5>الوقاية:</h5>
                <ul>
                    <li>تم إضافة فحوصات إضافية في جميع الصفحات</li>
                    <li>تم تحسين معالجة الأخطاء</li>
                    <li>تم إضافة آلية استرجاع تلقائية للبيانات</li>
                </ul>
                
                <div class="alert alert-info mt-3">
                    <strong>نصيحة:</strong> إذا واجهت هذه المشكلة مرة أخرى، قم بزيارة هذه الصفحة لإصلاحها تلقائياً.
                </div>
            </div>
        </div>

        <!-- تنظيف -->
        <div class="text-center text-white mt-4">
            <div class="alert alert-warning">
                <strong>ملاحظة:</strong> احذف هذا الملف بعد حل المشكلة:
                <br><code>fix-session-role.php</code>
            </div>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إعادة توجيه تلقائية بعد 10 ثوان إذا تم الإصلاح بنجاح
        <?php if ($message_type === 'success'): ?>
            setTimeout(function() {
                if (confirm('هل تريد الانتقال للصفحة الرئيسية؟')) {
                    window.location.href = 'index.php';
                }
            }, 5000);
        <?php endif; ?>
    </script>

</body>
</html>
