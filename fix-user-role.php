<?php
/**
 * إصلاح صلاحية المستخدم
 * Fix User Role
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "config/db_config.php";
require_once "includes/functions.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$message = '';
$message_type = '';

// معالجة تحديث الصلاحية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_role'])) {
    try {
        $user_id = intval($_POST['user_id']);
        $new_role = clean($conn, $_POST['new_role']);
        
        // التحقق من صحة الصلاحية
        $valid_roles = ['admin', 'manager', 'cashier'];
        if (!in_array($new_role, $valid_roles)) {
            throw new Exception("صلاحية غير صحيحة");
        }
        
        // تحديث صلاحية المستخدم
        $update_query = "UPDATE users SET role = ? WHERE id = ?";
        $stmt = $conn->prepare($update_query);
        $stmt->bind_param("si", $new_role, $user_id);
        $stmt->execute();
        
        if ($stmt->affected_rows > 0) {
            // تحديث الجلسة إذا كان المستخدم الحالي
            if ($user_id == $_SESSION['user_id']) {
                $_SESSION['role'] = $new_role;
            }
            
            $message = "تم تحديث الصلاحية بنجاح إلى: $new_role";
            $message_type = 'success';
        } else {
            $message = "لم يتم تحديث أي شيء";
            $message_type = 'warning';
        }
        
    } catch (Exception $e) {
        $message = "خطأ في تحديث الصلاحية: " . $e->getMessage();
        $message_type = 'danger';
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح صلاحية المستخدم - نظام Zero</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-user-cog me-3"></i>
                إصلاح صلاحية المستخدم
            </h1>
            <p class="lead">تحديث صلاحيات المستخدمين للوصول لإعادة ضبط المصنع</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?php echo ($message_type == 'success') ? 'check-circle' : 'exclamation-circle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION["user_id"])): ?>
            
            <!-- معلومات المستخدم الحالي -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3><i class="fas fa-user me-2"></i>المستخدم الحالي</h3>
                </div>
                <div class="card-body">
                    <?php
                    // الحصول على بيانات المستخدم الحالي
                    $user_id = $_SESSION["user_id"];
                    $user_query = "SELECT * FROM users WHERE id = ?";
                    $stmt = $conn->prepare($user_query);
                    $stmt->bind_param("i", $user_id);
                    $stmt->execute();
                    $user_result = $stmt->get_result();
                    $current_user = $user_result->fetch_assoc();
                    ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>البيانات الحالية:</h5>
                            <ul>
                                <li><strong>الاسم:</strong> <?php echo htmlspecialchars($current_user['name']); ?></li>
                                <li><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($current_user['username']); ?></li>
                                <li><strong>الصلاحية الحالية:</strong> 
                                    <span class="badge bg-<?php echo ($current_user['role'] == 'admin') ? 'success' : 'warning'; ?>">
                                        <?php echo htmlspecialchars($current_user['role']); ?>
                                    </span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>تحديث الصلاحية:</h5>
                            <form method="POST">
                                <input type="hidden" name="user_id" value="<?php echo $current_user['id']; ?>">
                                
                                <div class="mb-3">
                                    <label for="new_role" class="form-label">الصلاحية الجديدة:</label>
                                    <select class="form-select" id="new_role" name="new_role" required>
                                        <option value="admin" <?php echo ($current_user['role'] == 'admin') ? 'selected' : ''; ?>>
                                            مدير (admin) - صلاحية كاملة
                                        </option>
                                        <option value="manager" <?php echo ($current_user['role'] == 'manager') ? 'selected' : ''; ?>>
                                            مدير فرع (manager) - صلاحية محدودة
                                        </option>
                                        <option value="cashier" <?php echo ($current_user['role'] == 'cashier') ? 'selected' : ''; ?>>
                                            كاشير (cashier) - صلاحية أساسية
                                        </option>
                                    </select>
                                </div>
                                
                                <button type="submit" name="update_role" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i>
                                    تحديث الصلاحية
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <?php if ($current_user['role'] == 'admin'): ?>
                        <div class="alert alert-success mt-3">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>ممتاز!</strong> المستخدم الحالي له صلاحية مدير ويمكنه الوصول لإعادة ضبط المصنع.
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> المستخدم الحالي ليس له صلاحية مدير. يجب ترقيته إلى "مدير" للوصول لإعادة ضبط المصنع.
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- جميع المستخدمين -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h3><i class="fas fa-users me-2"></i>جميع المستخدمين</h3>
                </div>
                <div class="card-body">
                    <?php
                    $all_users_query = "SELECT * FROM users ORDER BY id";
                    $all_users_result = $conn->query($all_users_query);
                    ?>
                    
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>المعرف</th>
                                    <th>الاسم</th>
                                    <th>اسم المستخدم</th>
                                    <th>الصلاحية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($user = $all_users_result->fetch_assoc()): ?>
                                    <tr class="<?php echo ($user['id'] == $_SESSION['user_id']) ? 'table-warning' : ''; ?>">
                                        <td><?php echo $user['id']; ?></td>
                                        <td>
                                            <?php echo htmlspecialchars($user['name']); ?>
                                            <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                                <span class="badge bg-primary">أنت</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo ($user['role'] == 'admin') ? 'success' : (($user['role'] == 'manager') ? 'warning' : 'secondary'); ?>">
                                                <?php echo htmlspecialchars($user['role']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <input type="hidden" name="new_role" value="admin">
                                                <?php if ($user['role'] !== 'admin'): ?>
                                                    <button type="submit" name="update_role" class="btn btn-success btn-sm" 
                                                            onclick="return confirm('هل تريد ترقية هذا المستخدم إلى مدير؟')">
                                                        <i class="fas fa-arrow-up me-1"></i>ترقية لمدير
                                                    </button>
                                                <?php else: ?>
                                                    <span class="badge bg-success">مدير بالفعل</span>
                                                <?php endif; ?>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        <?php else: ?>
            
            <!-- المستخدم غير مسجل دخول -->
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h3><i class="fas fa-exclamation-triangle me-2"></i>غير مسجل دخول</h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-times-circle me-2"></i>يجب تسجيل الدخول أولاً</h5>
                        <p>لا يمكن تحديث الصلاحيات بدون تسجيل الدخول.</p>
                        <a href="login.php" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    </div>
                </div>
            </div>

        <?php endif; ?>

        <!-- معلومات الصلاحيات -->
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-info-circle me-2"></i>معلومات الصلاحيات</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h5><span class="badge bg-success">admin</span> مدير</h5>
                        <ul>
                            <li>صلاحية كاملة</li>
                            <li>الوصول لإعادة ضبط المصنع</li>
                            <li>إدارة المستخدمين</li>
                            <li>جميع الإعدادات</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5><span class="badge bg-warning">manager</span> مدير فرع</h5>
                        <ul>
                            <li>صلاحية محدودة</li>
                            <li>إدارة المبيعات والمشتريات</li>
                            <li>عرض التقارير</li>
                            <li>بعض الإعدادات</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5><span class="badge bg-secondary">cashier</span> كاشير</h5>
                        <ul>
                            <li>صلاحية أساسية</li>
                            <li>المبيعات فقط</li>
                            <li>عرض المنتجات</li>
                            <li>لا يمكن الوصول للإعدادات</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- روابط مفيدة -->
        <div class="text-center text-white mt-4">
            <h3>🔧 تم إصلاح مشكلة الصلاحية 🔧</h3>
            <p>يمكنك الآن الوصول لإعادة ضبط المصنع إذا كان لديك صلاحية مدير</p>
            
            <div class="row mt-4">
                <div class="col-md-3">
                    <a href="check-user-role.php" class="btn btn-light btn-lg w-100 mb-2">
                        <i class="fas fa-user-shield me-2"></i>فحص الصلاحية
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="pages/settings/factory-reset.php" class="btn btn-danger btn-lg w-100 mb-2">
                        <i class="fas fa-exclamation-triangle me-2"></i>إعادة ضبط المصنع
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="pages/settings/index.php" class="btn btn-secondary btn-lg w-100 mb-2">
                        <i class="fas fa-cogs me-2"></i>الإعدادات
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="index.php" class="btn btn-info btn-lg w-100 mb-2">
                        <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                    </a>
                </div>
            </div>
            
            <div class="alert alert-warning mt-4">
                <strong>ملاحظة:</strong> احذف ملفات الإصلاح بعد حل المشكلة:
                <br><code>check-user-role.php</code> و <code>fix-user-role.php</code>
            </div>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
