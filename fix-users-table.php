<?php
/**
 * إصلاح جدول المستخدمين - إضافة الأعمدة المفقودة
 * Fix Users Table - Add Missing Columns
 */

// استدعاء ملفات الإعدادات
require_once "config/db_config.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$message = '';
$message_type = '';
$fixes_applied = [];

try {
    // التحقق من هيكل الجدول الحالي
    $check_table = "DESCRIBE users";
    $result = $conn->query($check_table);
    
    $existing_columns = [];
    while ($row = $result->fetch_assoc()) {
        $existing_columns[] = $row['Field'];
    }
    
    // الأعمدة المطلوبة
    $required_columns = [
        'email' => "VARCHAR(255) NULL",
        'phone' => "VARCHAR(20) NULL", 
        'status' => "ENUM('active', 'inactive') DEFAULT 'active'",
        'created_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
        'updated_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
    ];
    
    // إضافة الأعمدة المفقودة
    foreach ($required_columns as $column => $definition) {
        if (!in_array($column, $existing_columns)) {
            $add_column_sql = "ALTER TABLE users ADD COLUMN $column $definition";
            if ($conn->query($add_column_sql)) {
                $fixes_applied[] = "تم إضافة عمود: $column";
            } else {
                throw new Exception("فشل في إضافة عمود $column: " . $conn->error);
            }
        } else {
            $fixes_applied[] = "العمود موجود بالفعل: $column";
        }
    }
    
    // التحقق من وجود مستخدم admin
    $admin_check = "SELECT COUNT(*) as count FROM users WHERE role = 'admin'";
    $admin_result = $conn->query($admin_check);
    $admin_count = $admin_result->fetch_assoc()['count'];
    
    if ($admin_count == 0) {
        // إنشاء مستخدم admin افتراضي
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $create_admin = "INSERT INTO users (name, username, password, role, email, status) VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($create_admin);
        $name = 'المدير العام';
        $username = 'admin';
        $role = 'admin';
        $email = '<EMAIL>';
        $status = 'active';
        $stmt->bind_param("ssssss", $name, $username, $admin_password, $role, $email, $status);
        
        if ($stmt->execute()) {
            $fixes_applied[] = "تم إنشاء مستخدم admin افتراضي (admin/admin123)";
        }
    }
    
    $message = "تم إصلاح جدول المستخدمين بنجاح";
    $message_type = 'success';
    
} catch (Exception $e) {
    $message = "خطأ في إصلاح الجدول: " . $e->getMessage();
    $message_type = 'danger';
}

// عرض هيكل الجدول الحالي
$table_structure = [];
try {
    $describe_result = $conn->query("DESCRIBE users");
    while ($row = $describe_result->fetch_assoc()) {
        $table_structure[] = $row;
    }
} catch (Exception $e) {
    // تجاهل الخطأ
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح جدول المستخدمين - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .fix-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 8px;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-database me-3"></i>
                إصلاح جدول المستخدمين
            </h1>
            <p class="lead">إضافة الأعمدة المفقودة وإصلاح هيكل الجدول</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?php echo ($message_type == 'success') ? 'check-circle' : 'exclamation-circle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- الإصلاحات المطبقة -->
        <?php if (!empty($fixes_applied)): ?>
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3><i class="fas fa-wrench me-2"></i>الإصلاحات المطبقة</h3>
                </div>
                <div class="card-body">
                    <?php foreach ($fixes_applied as $fix): ?>
                        <div class="fix-item">
                            <i class="fas fa-check text-success me-2"></i>
                            <?php echo $fix; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- هيكل الجدول الحالي -->
        <?php if (!empty($table_structure)): ?>
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h3><i class="fas fa-table me-2"></i>هيكل جدول المستخدمين الحالي</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>اسم العمود</th>
                                    <th>النوع</th>
                                    <th>Null</th>
                                    <th>Key</th>
                                    <th>Default</th>
                                    <th>Extra</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($table_structure as $column): ?>
                                    <tr>
                                        <td><code><?php echo $column['Field']; ?></code></td>
                                        <td><?php echo $column['Type']; ?></td>
                                        <td><?php echo $column['Null']; ?></td>
                                        <td><?php echo $column['Key']; ?></td>
                                        <td><?php echo $column['Default'] ?? 'NULL'; ?></td>
                                        <td><?php echo $column['Extra']; ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- معلومات إضافية -->
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>الأعمدة المضافة:</h5>
                        <ul>
                            <li><code>email</code> - البريد الإلكتروني (اختياري)</li>
                            <li><code>phone</code> - رقم الهاتف (اختياري)</li>
                            <li><code>status</code> - حالة المستخدم (نشط/معطل)</li>
                            <li><code>created_at</code> - تاريخ الإنشاء</li>
                            <li><code>updated_at</code> - تاريخ آخر تحديث</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>ملاحظات:</h5>
                        <ul>
                            <li>البريد الإلكتروني والهاتف اختياريان</li>
                            <li>الحالة الافتراضية هي "نشط"</li>
                            <li>التواريخ تُحدث تلقائياً</li>
                            <li>تم إنشاء مستخدم admin إذا لم يكن موجوداً</li>
                        </ul>
                    </div>
                </div>
                
                <?php if (in_array("تم إنشاء مستخدم admin افتراضي (admin/admin123)", $fixes_applied)): ?>
                    <div class="alert alert-warning mt-3">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>مستخدم Admin جديد:</h6>
                        <p class="mb-0">
                            <strong>اسم المستخدم:</strong> admin<br>
                            <strong>كلمة المرور:</strong> admin123<br>
                            <strong>يرجى تغيير كلمة المرور بعد تسجيل الدخول!</strong>
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- اختبار النظام -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-vial me-2"></i>اختبار النظام</h3>
            </div>
            <div class="card-body">
                <p>الآن يمكنك اختبار إضافة المستخدمين:</p>
                
                <div class="row">
                    <div class="col-md-4">
                        <a href="pages/settings/users.php" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-users-cog me-2"></i>
                            إدارة المستخدمين
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="login.php" class="btn btn-success w-100 mb-2">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </a>
                    </div>
                    <div class="col-md-4">
                        <a href="index.php" class="btn btn-info w-100 mb-2">
                            <i class="fas fa-home me-2"></i>
                            الصفحة الرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- SQL للمراجعة -->
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h3><i class="fas fa-code me-2"></i>استعلامات SQL المستخدمة</h3>
            </div>
            <div class="card-body">
                <h6>الأعمدة المضافة:</h6>
                <pre class="bg-light p-3 rounded"><code>ALTER TABLE users ADD COLUMN email VARCHAR(255) NULL;
ALTER TABLE users ADD COLUMN phone VARCHAR(20) NULL;
ALTER TABLE users ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active';
ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;</code></pre>
                
                <h6 class="mt-3">إنشاء مستخدم Admin:</h6>
                <pre class="bg-light p-3 rounded"><code>INSERT INTO users (name, username, password, role, email, status) 
VALUES ('المدير العام', 'admin', '[hashed_password]', 'admin', '<EMAIL>', 'active');</code></pre>
            </div>
        </div>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-4">
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>تم الإصلاح بنجاح!</h5>
                <p class="mb-0">يمكنك الآن إضافة المستخدمين بدون مشاكل</p>
            </div>
            
            <div class="alert alert-info">
                <strong>ملاحظة:</strong> احذف هذا الملف بعد التأكد من عمل النظام:
                <br><code>fix-users-table.php</code>
            </div>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
