<?php
// بداية الجلسة (إذا لم تكن بدأت بالفعل)
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// التحقق من تسجيل الدخول
function checkLogin() {
    if (!isset($_SESSION["user_id"])) {
        header("Location: login.php");
        exit();
    }
}

// الحصول على معلومات المستخدم الحالي
function getCurrentUser() {
    if (isset($_SESSION["user_id"])) {
        return [
            "id" => $_SESSION["user_id"],
            "name" => $_SESSION["user_name"],
            "username" => $_SESSION["username"],
            "role" => $_SESSION["user_role"]
        ];
    }
    return null;
}

// دالة للتحقق من صلاحيات المستخدم
function checkPermission($requiredRole) {
    if (!isset($_SESSION["user_role"]) || $_SESSION["user_role"] != $requiredRole && $_SESSION["user_role"] != "admin") {
        header("Location: ../unauthorized.php");
        exit();
    }
}

// الحصول على اسم الصفحة الحالية
$current_page = basename($_SERVER["PHP_SELF"]);
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . " | " : ""; ?>نظام Zero لإدارة المحلات</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo isset($base_url) ? $base_url : ""; ?>assets/css/style.css">
    <!-- نمط الخط العربي -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="<?php echo isset($base_url) ? $base_url : ""; ?>index.php">
                <i class="fas fa-store"></i> نظام Zero
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <?php if (isset($_SESSION["user_id"])): ?>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo ($current_page == 'index.php') ? 'active' : ''; ?>" href="<?php echo isset($base_url) ? $base_url : ""; ?>index.php">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos($current_page, 'sales') !== false) ? 'active' : ''; ?>" href="<?php echo isset($base_url) ? $base_url : ""; ?>pages/sales/index.php">
                            <i class="fas fa-shopping-cart"></i> المبيعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos($current_page, 'purchases') !== false) ? 'active' : ''; ?>" href="<?php echo isset($base_url) ? $base_url : ""; ?>pages/purchases/index.php">
                            <i class="fas fa-truck"></i> المشتريات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos($current_page, 'products') !== false) ? 'active' : ''; ?>" href="<?php echo isset($base_url) ? $base_url : ""; ?>pages/products/index.php">
                            <i class="fas fa-boxes"></i> المنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos($current_page, 'customers') !== false) ? 'active' : ''; ?>" href="<?php echo isset($base_url) ? $base_url : ""; ?>pages/customers/index.php">
                            <i class="fas fa-users"></i> العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos($current_page, 'suppliers') !== false) ? 'active' : ''; ?>" href="<?php echo isset($base_url) ? $base_url : ""; ?>pages/suppliers/index.php">
                            <i class="fas fa-industry"></i> الموردين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos($current_page, 'expenses') !== false) ? 'active' : ''; ?>" href="<?php echo isset($base_url) ? $base_url : ""; ?>pages/expenses/index.php">
                            <i class="fas fa-money-bill"></i> المصروفات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos($current_page, 'treasury') !== false) ? 'active' : ''; ?>" href="<?php echo isset($base_url) ? $base_url : ""; ?>pages/treasury/index.php">
                            <i class="fas fa-cash-register"></i> الخزينة
                        </a>
                    </li>
                    <?php if (isset($_SESSION["user_role"]) && $_SESSION["user_role"] == "admin"): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo (strpos($current_page, 'settings') !== false) ? 'active' : ''; ?>" href="<?php echo isset($base_url) ? $base_url : ""; ?>pages/settings/index.php">
                            <i class="fas fa-cogs"></i> الإعدادات
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
                <div class="d-flex">
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?php echo $_SESSION["user_name"]; ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="<?php echo isset($base_url) ? $base_url : ""; ?>profile.php"><i class="fas fa-user-cog"></i> الملف الشخصي</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="<?php echo isset($base_url) ? $base_url : ""; ?>logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </nav>

    <!-- محتوى الصفحة الرئيسي -->
    <div class="container mt-4">
        <?php if (isset($page_title)): ?>
        <div class="row mb-3">
            <div class="col">
                <h2 class="page-title"><i class="<?php echo isset($page_icon) ? $page_icon : 'fas fa-file'; ?>"></i> <?php echo $page_title; ?></h2>
            </div>
        </div>
        <?php endif; ?>
