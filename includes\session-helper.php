<?php
/**
 * مساعد الجلسة - Session Helper
 * يضمن التوافق والأمان في إدارة الجلسات
 */

// دالة للتحقق من تسجيل الدخول
function requireLogin() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION["user_id"])) {
        header("Location: " . getLoginUrl());
        exit();
    }
}

// دالة للحصول على رابط تسجيل الدخول حسب المسار الحالي
function getLoginUrl() {
    $current_path = $_SERVER['PHP_SELF'];
    $depth = substr_count($current_path, '/') - 1;
    
    $login_url = str_repeat('../', $depth) . 'login.php';
    return $login_url;
}

// دالة للحصول على دور المستخدم الحالي
function getCurrentUserRole() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    // التحقق من وجود الدور في الجلسة
    if (isset($_SESSION["role"])) {
        return $_SESSION["role"];
    } elseif (isset($_SESSION["user_role"])) {
        // نسخ من user_role إلى role للتوافق
        $_SESSION["role"] = $_SESSION["user_role"];
        return $_SESSION["user_role"];
    }
    
    // إذا لم يوجد الدور، محاولة استرجاعه من قاعدة البيانات
    if (isset($_SESSION["user_id"])) {
        return refreshUserRole($_SESSION["user_id"]);
    }
    
    return null;
}

// دالة لتحديث دور المستخدم من قاعدة البيانات
function refreshUserRole($user_id) {
    global $conn;
    
    if (!$conn) {
        return null;
    }
    
    $query = "SELECT role FROM users WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $_SESSION["role"] = $user["role"];
        $_SESSION["user_role"] = $user["role"]; // للتوافق
        return $user["role"];
    }
    
    return null;
}

// دالة للتحقق من الصلاحيات
function requireRole($required_role) {
    requireLogin();
    
    $current_role = getCurrentUserRole();
    
    // المدير له جميع الصلاحيات
    if ($current_role === 'admin') {
        return true;
    }
    
    // التحقق من الدور المطلوب
    if ($current_role !== $required_role) {
        $_SESSION['error_message'] = "غير مسموح لك بالوصول لهذه الصفحة";
        header("Location: " . getIndexUrl());
        exit();
    }
    
    return true;
}

// دالة للتحقق من صلاحية المدير فقط
function requireAdmin() {
    requireLogin();
    
    $current_role = getCurrentUserRole();
    
    if ($current_role !== 'admin') {
        $_SESSION['error_message'] = "غير مسموح لك بالوصول لهذه الصفحة";
        header("Location: " . getIndexUrl());
        exit();
    }
    
    return true;
}

// دالة للحصول على رابط الصفحة الرئيسية حسب المسار الحالي
function getIndexUrl() {
    $current_path = $_SERVER['PHP_SELF'];
    $depth = substr_count($current_path, '/') - 1;
    
    $index_url = str_repeat('../', $depth) . 'index.php';
    return $index_url;
}

// دالة للتحقق من وجود صلاحية معينة
function hasPermission($permission) {
    $current_role = getCurrentUserRole();
    
    if (!$current_role) {
        return false;
    }
    
    // المدير له جميع الصلاحيات
    if ($current_role === 'admin') {
        return true;
    }
    
    // تحديد الصلاحيات لكل دور
    $permissions = [
        'admin' => ['*'], // جميع الصلاحيات
        'manager' => [
            'sales', 'purchases', 'products', 'customers', 
            'suppliers', 'expenses', 'treasury', 'reports'
        ],
        'cashier' => [
            'sales', 'products_view', 'customers_view'
        ]
    ];
    
    if (!isset($permissions[$current_role])) {
        return false;
    }
    
    $user_permissions = $permissions[$current_role];
    
    // إذا كان لديه صلاحية شاملة
    if (in_array('*', $user_permissions)) {
        return true;
    }
    
    // التحقق من الصلاحية المحددة
    return in_array($permission, $user_permissions);
}

// دالة لعرض معلومات المستخدم الحالي
function getCurrentUserInfo() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (!isset($_SESSION["user_id"])) {
        return null;
    }
    
    return [
        'id' => $_SESSION["user_id"],
        'name' => $_SESSION["user_name"] ?? 'غير محدد',
        'username' => $_SESSION["username"] ?? 'غير محدد',
        'role' => getCurrentUserRole() ?? 'غير محدد'
    ];
}

// دالة لتنظيف الجلسة عند تسجيل الخروج
function cleanupSession() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    // حذف جميع متغيرات الجلسة
    $_SESSION = array();
    
    // حذف كوكيز الجلسة
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // تدمير الجلسة
    session_destroy();
}

// دالة للتحقق من صحة الجلسة
function validateSession() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    // التحقق من وجود معرف المستخدم
    if (!isset($_SESSION["user_id"])) {
        return false;
    }
    
    // التحقق من وجود الدور
    $role = getCurrentUserRole();
    if (!$role) {
        return false;
    }
    
    // التحقق من صحة البيانات في قاعدة البيانات
    global $conn;
    if ($conn) {
        $query = "SELECT id FROM users WHERE id = ? AND status = 'active'";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $_SESSION["user_id"]);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            cleanupSession();
            return false;
        }
    }
    
    return true;
}

// دالة لإعادة تحميل بيانات المستخدم
function refreshUserData() {
    if (!isset($_SESSION["user_id"])) {
        return false;
    }
    
    global $conn;
    if (!$conn) {
        return false;
    }
    
    $query = "SELECT * FROM users WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $_SESSION["user_id"]);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        
        // تحديث بيانات الجلسة
        $_SESSION["user_name"] = $user["name"];
        $_SESSION["username"] = $user["username"];
        $_SESSION["role"] = $user["role"];
        $_SESSION["user_role"] = $user["role"]; // للتوافق
        
        return true;
    }
    
    return false;
}

// دالة لعرض اسم الدور بالعربية
function getRoleDisplayName($role) {
    $roles = [
        'admin' => 'مدير',
        'manager' => 'مدير فرع',
        'cashier' => 'كاشير'
    ];
    
    return $roles[$role] ?? $role;
}

// دالة للتحقق من انتهاء صلاحية الجلسة
function checkSessionTimeout($timeout_minutes = 120) {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    
    if (isset($_SESSION['last_activity'])) {
        $inactive_time = time() - $_SESSION['last_activity'];
        
        if ($inactive_time > ($timeout_minutes * 60)) {
            cleanupSession();
            return false;
        }
    }
    
    $_SESSION['last_activity'] = time();
    return true;
}
?>
