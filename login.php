<?php
// استدعاء ملف الإعدادات
require_once "config/db_config.php";

// بدء الجلسة
session_start();

// التحقق إذا كان المستخدم مسجل الدخول بالفعل
if (isset($_SESSION["user_id"])) {
    header("Location: index.php");
    exit();
}

// التعامل مع نموذج تسجيل الدخول
$login_error = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = clean($conn, $_POST["username"]);
    $password = $_POST["password"];

    // التحقق من اسم المستخدم وكلمة المرور
    $query = "SELECT * FROM users WHERE username = '$username' LIMIT 1";
    $result = $conn->query($query);

    if ($result && $result->num_rows > 0) {
        $user = $result->fetch_assoc();

        // التحقق من كلمة المرور
        if (password_verify($password, $user["password"])) {
            // تسجيل الدخول ناجح
            $_SESSION["user_id"] = $user["id"];
            $_SESSION["username"] = $user["username"];
            $_SESSION["user_name"] = $user["name"];
            $_SESSION["user_role"] = $user["role"];
            $_SESSION["role"] = $user["role"]; // إضافة للتوافق

            // إعادة توجيه إلى الصفحة الرئيسية
            header("Location: index.php");
            exit();
        } else {
            $login_error = "كلمة المرور غير صحيحة";
        }
    } else {
        $login_error = "اسم المستخدم غير موجود";
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول | نظام Zero لإدارة المحلات</title>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <!-- نمط الخط العربي -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="login-container">
        <div class="login-card">
            <div class="login-logo">
                <i class="fas fa-store"></i>
                <h2 class="mt-3">نظام Zero</h2>
                <p class="text-muted">لإدارة المحلات</p>
            </div>

            <?php if (!empty($login_error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i> <?php echo $login_error; ?>
            </div>
            <?php endif; ?>

            <form method="post" action="login.php">
                <div class="mb-3">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                        <input type="text" class="form-control" id="username" name="username" required autofocus>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                </div>

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i> تسجيل الدخول
                    </button>
                </div>
            </form>

            <div class="mt-4 text-center text-muted small">
                <p>جميع الحقوق محفوظة &copy; <?php echo date('Y'); ?> | نظام Zero لإدارة المحلات</p>
            </div>
        </div>
    </div>

    <!-- سكربتات JavaScript -->
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>