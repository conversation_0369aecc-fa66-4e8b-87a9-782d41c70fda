<?php
// بدء الجلسة
session_start();

// حذف متغيرات الجلسة
$_SESSION = array();

// إذا كانت هناك كوكيز للجلسة
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(
        session_name(),
        '',
        time() - 42000,
        $params["path"],
        $params["domain"],
        $params["secure"],
        $params["httponly"]
    );
}

// إنهاء الجلسة
session_destroy();

// إعادة توجيه إلى صفحة تسجيل الدخول
header("Location: login.php");
exit();
?>