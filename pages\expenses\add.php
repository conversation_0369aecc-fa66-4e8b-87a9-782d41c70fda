<?php
/**
 * صفحة إضافة مصروف جديد
 * Add New Expense Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "إضافة مصروف جديد";
$page_icon = "fas fa-plus-circle";

// معالجة إضافة المصروف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_expense'])) {
    try {
        $conn->begin_transaction();
        
        // بيانات المصروف
        $expense_type = clean($conn, $_POST['expense_type']);
        $amount = floatval($_POST['amount']);
        $notes = clean($conn, $_POST['notes']);
        $expense_date = clean($conn, $_POST['expense_date']);
        
        // التحقق من صحة البيانات
        if (empty($expense_type)) {
            throw new Exception("نوع المصروف مطلوب");
        }
        
        if ($amount <= 0) {
            throw new Exception("المبلغ يجب أن يكون أكبر من صفر");
        }
        
        // التحقق من كفاية الرصيد في الخزينة
        $current_balance = getTreasuryBalance();
        if ($current_balance < $amount) {
            throw new Exception("الرصيد في الخزينة غير كافي. الرصيد الحالي: " . formatMoney($current_balance));
        }
        
        // إدراج المصروف
        $insert_query = "INSERT INTO expenses (expense_type, amount, notes, user_id, expense_date) 
                        VALUES (?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param("sdsss", $expense_type, $amount, $notes, $_SESSION['user_id'], $expense_date);
        $stmt->execute();
        
        $expense_id = $conn->insert_id;
        
        // تحديث الخزينة (خصم من الخزينة)
        $description = "مصروف: $expense_type";
        if (!addTreasuryTransaction('expenses', $expense_id, $amount, $description, $_SESSION['user_id'], $expense_date)) {
            throw new Exception("خطأ في تحديث الخزينة");
        }
        
        $conn->commit();
        $_SESSION['success_message'] = "تم إضافة المصروف بنجاح";
        header("Location: index.php");
        exit();
        
    } catch (Exception $e) {
        $conn->rollback();
        $_SESSION['error_message'] = "خطأ في إضافة المصروف: " . $e->getMessage();
    }
}

// الحصول على أنواع المصروفات الشائعة
$common_types = [
    'إيجار',
    'كهرباء',
    'مياه',
    'هاتف وإنترنت',
    'رواتب',
    'صيانة',
    'وقود',
    'مواصلات',
    'قرطاسية',
    'تنظيف',
    'أمن وحراسة',
    'تأمينات',
    'ضرائب',
    'مصروفات إدارية',
    'أخرى'
];

// الحصول على أنواع المصروفات المستخدمة مسبقاً
$used_types_query = "SELECT DISTINCT expense_type FROM expenses ORDER BY expense_type";
$used_types_result = $conn->query($used_types_query);
$used_types = [];
while ($row = $used_types_result->fetch_assoc()) {
    $used_types[] = $row['expense_type'];
}

// دمج الأنواع الشائعة مع المستخدمة
$all_types = array_unique(array_merge($common_types, $used_types));
sort($all_types);

// الرصيد الحالي في الخزينة
$current_balance = getTreasuryBalance();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .form-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
        .balance-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        .required {
            color: #dc3545;
        }
        .expense-type-suggestions {
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">إضافة مصروف جديد وخصمه من الخزينة</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="index.php" class="btn btn-light btn-lg">
                        <i class="fas fa-list me-2"></i>
                        قائمة المصروفات
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg ms-2">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- الرصيد الحالي -->
        <div class="balance-card">
            <h4 class="mb-2">
                <i class="fas fa-wallet me-2"></i>
                الرصيد المتاح في الخزينة
            </h4>
            <h2 class="mb-0"><?php echo formatMoney($current_balance); ?></h2>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-container">
                    <h4 class="mb-4">
                        <i class="fas fa-money-bill-alt me-2 text-danger"></i>
                        بيانات المصروف
                    </h4>
                    
                    <form method="POST" id="expenseForm">
                        <div class="row">
                            
                            <!-- نوع المصروف -->
                            <div class="col-md-6 mb-3">
                                <label for="expense_type" class="form-label">نوع المصروف <span class="required">*</span></label>
                                <input type="text" class="form-control" id="expense_type" name="expense_type" 
                                       list="expense_types" required autocomplete="off">
                                <datalist id="expense_types">
                                    <?php foreach ($all_types as $type): ?>
                                        <option value="<?php echo htmlspecialchars($type); ?>">
                                    <?php endforeach; ?>
                                </datalist>
                            </div>
                            
                            <!-- المبلغ -->
                            <div class="col-md-6 mb-3">
                                <label for="amount" class="form-label">المبلغ <span class="required">*</span></label>
                                <input type="number" class="form-control" id="amount" name="amount" 
                                       step="0.01" min="0.01" max="<?php echo $current_balance; ?>" required>
                                <small class="text-muted">الحد الأقصى: <?php echo formatMoney($current_balance); ?></small>
                            </div>
                            
                            <!-- تاريخ المصروف -->
                            <div class="col-md-6 mb-3">
                                <label for="expense_date" class="form-label">تاريخ المصروف <span class="required">*</span></label>
                                <input type="date" class="form-control" id="expense_date" name="expense_date" 
                                       value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                            
                            <!-- الملاحظات -->
                            <div class="col-md-12 mb-4">
                                <label for="notes" class="form-label">الملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="4" 
                                          placeholder="تفاصيل إضافية عن المصروف (اختياري)"></textarea>
                            </div>
                            
                            <!-- أزرار الحفظ -->
                            <div class="col-md-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="index.php" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                    <button type="submit" name="add_expense" class="btn btn-danger">
                                        <i class="fas fa-save me-2"></i>حفظ المصروف
                                    </button>
                                </div>
                            </div>
                            
                        </div>
                    </form>
                </div>
                
                <!-- أنواع المصروفات الشائعة -->
                <div class="form-container mt-4">
                    <h5 class="mb-3">
                        <i class="fas fa-lightbulb me-2 text-warning"></i>
                        أنواع المصروفات الشائعة
                    </h5>
                    <div class="row">
                        <?php foreach ($common_types as $type): ?>
                            <div class="col-md-3 col-sm-4 col-6 mb-2">
                                <button type="button" class="btn btn-outline-secondary btn-sm w-100" 
                                        onclick="selectExpenseType('<?php echo htmlspecialchars($type); ?>')">
                                    <?php echo htmlspecialchars($type); ?>
                                </button>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // اختيار نوع المصروف من الأزرار
        function selectExpenseType(type) {
            document.getElementById('expense_type').value = type;
            document.getElementById('expense_type').focus();
        }
        
        // التحقق من صحة النموذج
        document.getElementById('expenseForm').addEventListener('submit', function(e) {
            const expenseType = document.getElementById('expense_type').value.trim();
            const amount = parseFloat(document.getElementById('amount').value) || 0;
            const maxAmount = <?php echo $current_balance; ?>;
            
            if (!expenseType) {
                e.preventDefault();
                alert('نوع المصروف مطلوب');
                return false;
            }
            
            if (amount <= 0) {
                e.preventDefault();
                alert('المبلغ يجب أن يكون أكبر من صفر');
                return false;
            }
            
            if (amount > maxAmount) {
                e.preventDefault();
                alert('المبلغ أكبر من الرصيد المتاح في الخزينة');
                return false;
            }
            
            // تأكيد الإضافة
            if (!confirm('هل أنت متأكد من إضافة هذا المصروف؟\n\nالنوع: ' + expenseType + '\nالمبلغ: ' + amount.toFixed(2) + ' ريال\n\nسيتم خصم المبلغ من الخزينة.')) {
                e.preventDefault();
                return false;
            }
        });
        
        // تحديث الحد الأقصى للمبلغ عند تغيير التاريخ (يمكن إضافة منطق إضافي هنا)
        document.getElementById('expense_date').addEventListener('change', function() {
            // يمكن إضافة منطق للتحقق من الرصيد في تاريخ معين
        });
        
        // تنسيق المبلغ أثناء الكتابة
        document.getElementById('amount').addEventListener('input', function(e) {
            const value = parseFloat(e.target.value) || 0;
            const maxAmount = <?php echo $current_balance; ?>;
            
            if (value > maxAmount) {
                e.target.setCustomValidity('المبلغ أكبر من الرصيد المتاح');
            } else {
                e.target.setCustomValidity('');
            }
        });
    </script>

</body>
</html>
