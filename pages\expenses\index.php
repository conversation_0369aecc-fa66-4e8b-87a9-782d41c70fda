<?php
/**
 * صفحة إدارة المصروفات الرئيسية
 * Expenses Management Main Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "إدارة المصروفات";
$page_icon = "fas fa-money-bill-alt";

// معالجة حذف المصروف
if (isset($_POST['delete_expense'])) {
    $expense_id = clean($conn, $_POST['expense_id']);
    
    try {
        $conn->begin_transaction();
        
        // حذف المصروف من الخزينة أولاً
        deleteTreasuryTransaction('expenses', $expense_id);
        
        // حذف المصروف
        $delete_query = "DELETE FROM expenses WHERE id = ?";
        $stmt = $conn->prepare($delete_query);
        $stmt->bind_param("i", $expense_id);
        $stmt->execute();
        
        $conn->commit();
        $_SESSION['success_message'] = "تم حذف المصروف بنجاح";
        
    } catch (Exception $e) {
        $conn->rollback();
        $_SESSION['error_message'] = "خطأ في حذف المصروف: " . $e->getMessage();
    }
    
    header("Location: index.php");
    exit();
}

// الحصول على المصروفات مع الفلترة والبحث
$search = isset($_GET['search']) ? clean($conn, $_GET['search']) : '';
$date_from = isset($_GET['date_from']) ? clean($conn, $_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? clean($conn, $_GET['date_to']) : '';
$expense_type = isset($_GET['expense_type']) ? clean($conn, $_GET['expense_type']) : '';

$where_conditions = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_conditions[] = "(e.expense_type LIKE ? OR e.notes LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= 'ss';
}

if (!empty($date_from)) {
    $where_conditions[] = "e.expense_date >= ?";
    $params[] = $date_from;
    $types .= 's';
}

if (!empty($date_to)) {
    $where_conditions[] = "e.expense_date <= ?";
    $params[] = $date_to;
    $types .= 's';
}

if (!empty($expense_type)) {
    $where_conditions[] = "e.expense_type = ?";
    $params[] = $expense_type;
    $types .= 's';
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
}

// استعلام المصروفات
$query = "SELECT e.*, u.name as user_name 
          FROM expenses e 
          LEFT JOIN users u ON e.user_id = u.id 
          $where_clause 
          ORDER BY e.expense_date DESC, e.id DESC";

if (!empty($params)) {
    $stmt = $conn->prepare($query);
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $expenses_result = $stmt->get_result();
} else {
    $expenses_result = $conn->query($query);
}

// الحصول على أنواع المصروفات للفلتر
$expense_types_query = "SELECT DISTINCT expense_type FROM expenses ORDER BY expense_type";
$expense_types_result = $conn->query($expense_types_query);

// حساب الإحصائيات
$stats_query = "SELECT 
                COUNT(*) as total_expenses,
                COALESCE(SUM(amount), 0) as total_amount,
                COUNT(DISTINCT expense_type) as expense_types_count
                FROM expenses e $where_clause";

if (!empty($params)) {
    $stmt = $conn->prepare($stats_query);
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $stats = $stmt->get_result()->fetch_assoc();
} else {
    $stats = $conn->query($stats_query)->fetch_assoc();
}

// إحصائيات حسب النوع
$type_stats_query = "SELECT expense_type, COUNT(*) as count, SUM(amount) as total 
                     FROM expenses e $where_clause 
                     GROUP BY expense_type 
                     ORDER BY total DESC";

if (!empty($params)) {
    $stmt = $conn->prepare($type_stats_query);
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $type_stats_result = $stmt->get_result();
} else {
    $type_stats_result = $conn->query($type_stats_query);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .btn-action {
            padding: 0.25rem 0.5rem;
            margin: 0 0.1rem;
            border-radius: 0.375rem;
        }
        .search-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .expense-amount {
            color: #dc3545;
            font-weight: bold;
        }
        .type-stats-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">إدارة ومتابعة جميع المصروفات والنفقات</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="add.php" class="btn btn-light btn-lg">
                        <i class="fas fa-plus me-2"></i>
                        مصروف جديد
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg ms-2">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-danger mb-2">
                            <i class="fas fa-money-bill-alt fa-2x"></i>
                        </div>
                        <h3 class="card-title text-danger"><?php echo number_format($stats['total_expenses']); ?></h3>
                        <p class="card-text text-muted">إجمالي المصروفات</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-warning mb-2">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                        <h3 class="card-title text-warning"><?php echo formatMoney($stats['total_amount']); ?></h3>
                        <p class="card-text text-muted">إجمالي المبلغ</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-info mb-2">
                            <i class="fas fa-tags fa-2x"></i>
                        </div>
                        <h3 class="card-title text-info"><?php echo number_format($stats['expense_types_count']); ?></h3>
                        <p class="card-text text-muted">أنواع المصروفات</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات حسب النوع -->
        <?php if ($type_stats_result->num_rows > 0): ?>
            <div class="type-stats-card">
                <h4 class="mb-4">
                    <i class="fas fa-chart-pie me-2 text-danger"></i>
                    المصروفات حسب النوع
                </h4>
                <div class="row">
                    <?php while ($type_stat = $type_stats_result->fetch_assoc()): ?>
                        <div class="col-md-4 mb-3">
                            <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded">
                                <div>
                                    <strong><?php echo htmlspecialchars($type_stat['expense_type']); ?></strong>
                                    <br><small class="text-muted"><?php echo $type_stat['count']; ?> مصروف</small>
                                </div>
                                <div class="text-danger fw-bold">
                                    <?php echo formatMoney($type_stat['total']); ?>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- البحث والفلترة -->
        <div class="search-container">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="نوع المصروف أو الملاحظات">
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                <div class="col-md-3">
                    <label for="expense_type" class="form-label">نوع المصروف</label>
                    <select class="form-select" id="expense_type" name="expense_type">
                        <option value="">جميع الأنواع</option>
                        <?php while ($type = $expense_types_result->fetch_assoc()): ?>
                            <option value="<?php echo htmlspecialchars($type['expense_type']); ?>" 
                                    <?php echo ($expense_type == $type['expense_type']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($type['expense_type']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                    </div>
                </div>
            </form>
            
            <?php if (!empty($search) || !empty($date_from) || !empty($date_to) || !empty($expense_type)): ?>
                <div class="mt-3">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i> مسح الفلاتر
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- جدول المصروفات -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>التاريخ</th>
                            <th>نوع المصروف</th>
                            <th>المبلغ</th>
                            <th>الملاحظات</th>
                            <th>المستخدم</th>
                            <th>وقت الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($expenses_result->num_rows > 0): ?>
                            <?php while ($expense = $expenses_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo date('Y-m-d', strtotime($expense['expense_date'])); ?></td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            <?php echo htmlspecialchars($expense['expense_type']); ?>
                                        </span>
                                    </td>
                                    <td class="expense-amount">
                                        <?php echo formatMoney($expense['amount']); ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($expense['notes'] ?? '-'); ?></td>
                                    <td><?php echo htmlspecialchars($expense['user_name']); ?></td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($expense['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="edit.php?id=<?php echo $expense['id']; ?>" 
                                               class="btn btn-warning btn-action" 
                                               data-bs-toggle="tooltip" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-danger btn-action" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#deleteModal<?php echo $expense['id']; ?>"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Modal حذف المصروف -->
                                        <div class="modal fade" id="deleteModal<?php echo $expense['id']; ?>" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">تأكيد الحذف</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>هل أنت متأكد من حذف المصروف <strong><?php echo htmlspecialchars($expense['expense_type']); ?></strong> بمبلغ <strong><?php echo formatMoney($expense['amount']); ?></strong>؟</p>
                                                        <p class="text-warning"><small>ملاحظة: سيتم أيضاً حذف المعاملة من الخزينة.</small></p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="expense_id" value="<?php echo $expense['id']; ?>">
                                                            <button type="submit" name="delete_expense" class="btn btn-danger">حذف</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="fas fa-money-bill-alt fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد مصروفات</h5>
                                    <p class="text-muted">لم يتم العثور على أي مصروفات تطابق معايير البحث</p>
                                    <a href="add.php" class="btn btn-danger">
                                        <i class="fas fa-plus me-2"></i>إضافة مصروف جديد
                                    </a>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تفعيل التلميحات
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    </script>

</body>
</html>
