<?php
/**
 * صفحة إضافة منتج جديد
 * Add New Product Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "إضافة منتج جديد";
$page_icon = "fas fa-plus-circle";

// معالجة إضافة المنتج
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_product'])) {
    try {
        // بيانات المنتج
        $name = clean($conn, $_POST['name']);
        $barcode = clean($conn, $_POST['barcode']);
        $description = clean($conn, $_POST['description']);
        $category_id = !empty($_POST['category_id']) ? intval($_POST['category_id']) : null;
        $cost_price = floatval($_POST['cost_price']);
        $selling_price = floatval($_POST['selling_price']);
        $stock_quantity = floatval($_POST['stock_quantity']);
        $min_stock = floatval($_POST['min_stock']);
        $unit = clean($conn, $_POST['unit']);
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        // التحقق من صحة البيانات
        if (empty($name)) {
            throw new Exception("اسم المنتج مطلوب");
        }
        
        if ($cost_price < 0 || $selling_price < 0) {
            throw new Exception("الأسعار يجب أن تكون أكبر من أو تساوي صفر");
        }
        
        if ($stock_quantity < 0 || $min_stock < 0) {
            throw new Exception("الكميات يجب أن تكون أكبر من أو تساوي صفر");
        }
        
        // التحقق من عدم تكرار الباركود
        if (!empty($barcode)) {
            $check_barcode = $conn->prepare("SELECT id FROM products WHERE barcode = ? AND id != 0");
            $check_barcode->bind_param("s", $barcode);
            $check_barcode->execute();
            if ($check_barcode->get_result()->num_rows > 0) {
                throw new Exception("الباركود موجود مسبقاً");
            }
        }
        
        // معالجة رفع الصورة
        $image_name = null;
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = "../../uploads/products/";
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
            $max_size = 5 * 1024 * 1024; // 5MB
            
            if (!in_array($_FILES['image']['type'], $allowed_types)) {
                throw new Exception("نوع الملف غير مدعوم. يُسمح بـ JPEG, PNG, GIF فقط");
            }
            
            if ($_FILES['image']['size'] > $max_size) {
                throw new Exception("حجم الملف كبير جداً. الحد الأقصى 5MB");
            }
            
            $file_extension = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
            $image_name = uniqid() . '.' . $file_extension;
            $upload_path = $upload_dir . $image_name;
            
            if (!move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                throw new Exception("فشل في رفع الصورة");
            }
        }
        
        // إدراج المنتج
        $insert_query = "INSERT INTO products (name, barcode, description, category_id, cost_price, selling_price, stock_quantity, min_stock, unit, image, is_active) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param("sssiidddssi", $name, $barcode, $description, $category_id, $cost_price, $selling_price, $stock_quantity, $min_stock, $unit, $image_name, $is_active);
        $stmt->execute();
        
        $_SESSION['success_message'] = "تم إضافة المنتج بنجاح";
        header("Location: index.php");
        exit();
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = "خطأ في إضافة المنتج: " . $e->getMessage();
        // حذف الصورة في حالة الخطأ
        if (isset($image_name) && file_exists("../../uploads/products/" . $image_name)) {
            unlink("../../uploads/products/" . $image_name);
        }
    }
}

// الحصول على قائمة الفئات
$categories_query = "SELECT id, name FROM categories ORDER BY name";
$categories_result = $conn->query($categories_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .form-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
        .image-preview {
            width: 150px;
            height: 150px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .image-preview:hover {
            border-color: #6f42c1;
            background-color: #f8f9fa;
        }
        .image-preview img {
            max-width: 100%;
            max-height: 100%;
            border-radius: 8px;
        }
        .required {
            color: #dc3545;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">إضافة منتج جديد إلى المخزون</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="index.php" class="btn btn-light btn-lg">
                        <i class="fas fa-list me-2"></i>
                        قائمة المنتجات
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg ms-2">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-container">
                    <h4 class="mb-4">
                        <i class="fas fa-box me-2" style="color: #6f42c1;"></i>
                        بيانات المنتج
                    </h4>
                    
                    <form method="POST" enctype="multipart/form-data" id="productForm">
                        <div class="row">
                            
                            <!-- المعلومات الأساسية -->
                            <div class="col-md-8">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">اسم المنتج <span class="required">*</span></label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="barcode" class="form-label">الباركود</label>
                                        <input type="text" class="form-control" id="barcode" name="barcode">
                                    </div>
                                    
                                    <div class="col-md-12 mb-3">
                                        <label for="description" class="form-label">الوصف</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="category_id" class="form-label">الفئة</label>
                                        <select class="form-select" id="category_id" name="category_id">
                                            <option value="">اختر الفئة</option>
                                            <?php while ($category = $categories_result->fetch_assoc()): ?>
                                                <option value="<?php echo $category['id']; ?>">
                                                    <?php echo htmlspecialchars($category['name']); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="unit" class="form-label">الوحدة</label>
                                        <input type="text" class="form-control" id="unit" name="unit" value="قطعة">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- صورة المنتج -->
                            <div class="col-md-4 mb-3">
                                <label class="form-label">صورة المنتج</label>
                                <div class="image-preview" onclick="document.getElementById('image').click()">
                                    <div class="text-center" id="imagePreview">
                                        <i class="fas fa-camera fa-2x text-muted mb-2"></i>
                                        <p class="text-muted mb-0">انقر لاختيار صورة</p>
                                    </div>
                                </div>
                                <input type="file" class="d-none" id="image" name="image" accept="image/*" onchange="previewImage(this)">
                                <small class="text-muted">الحد الأقصى: 5MB (JPEG, PNG, GIF)</small>
                            </div>
                            
                            <!-- الأسعار والمخزون -->
                            <div class="col-md-3 mb-3">
                                <label for="cost_price" class="form-label">سعر التكلفة <span class="required">*</span></label>
                                <input type="number" class="form-control" id="cost_price" name="cost_price" 
                                       step="0.01" min="0" required>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="selling_price" class="form-label">سعر البيع <span class="required">*</span></label>
                                <input type="number" class="form-control" id="selling_price" name="selling_price" 
                                       step="0.01" min="0" required>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="stock_quantity" class="form-label">الكمية الحالية</label>
                                <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" 
                                       step="0.001" min="0" value="0">
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="min_stock" class="form-label">الحد الأدنى للمخزون</label>
                                <input type="number" class="form-control" id="min_stock" name="min_stock" 
                                       step="0.001" min="0" value="5">
                            </div>
                            
                            <!-- حالة المنتج -->
                            <div class="col-md-12 mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                    <label class="form-check-label" for="is_active">
                                        منتج نشط (متاح للبيع)
                                    </label>
                                </div>
                            </div>
                            
                            <!-- أزرار الحفظ -->
                            <div class="col-md-12">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="index.php" class="btn btn-secondary me-md-2">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                    <button type="submit" name="add_product" class="btn" style="background-color: #6f42c1; color: white;">
                                        <i class="fas fa-save me-2"></i>حفظ المنتج
                                    </button>
                                </div>
                            </div>
                            
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // معاينة الصورة
        function previewImage(input) {
            const preview = document.getElementById('imagePreview');
            
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    preview.innerHTML = '<img src="' + e.target.result + '" alt="معاينة الصورة">';
                }
                
                reader.readAsDataURL(input.files[0]);
            }
        }
        
        // حساب هامش الربح تلقائياً
        document.getElementById('cost_price').addEventListener('input', calculateProfit);
        document.getElementById('selling_price').addEventListener('input', calculateProfit);
        
        function calculateProfit() {
            const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
            const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
            
            if (costPrice > 0 && sellingPrice > 0) {
                const profit = sellingPrice - costPrice;
                const profitMargin = ((profit / costPrice) * 100).toFixed(2);
                
                // يمكن إضافة عرض هامش الربح هنا إذا أردت
                console.log('هامش الربح: ' + profitMargin + '%');
            }
        }
        
        // التحقق من صحة النموذج
        document.getElementById('productForm').addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
            const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
            
            if (!name) {
                e.preventDefault();
                alert('اسم المنتج مطلوب');
                return false;
            }
            
            if (costPrice < 0 || sellingPrice < 0) {
                e.preventDefault();
                alert('الأسعار يجب أن تكون أكبر من أو تساوي صفر');
                return false;
            }
        });
    </script>

</body>
</html>
