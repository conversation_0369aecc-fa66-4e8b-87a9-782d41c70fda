<?php
/**
 * صفحة إدارة المنتجات الرئيسية
 * Products Management Main Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "إدارة المنتجات";
$page_icon = "fas fa-box";

// معالجة حذف المنتج
if (isset($_POST['delete_product'])) {
    $product_id = clean($conn, $_POST['product_id']);
    
    try {
        // التحقق من وجود مبيعات أو مشتريات للمنتج
        $check_sales = $conn->query("SELECT COUNT(*) as count FROM sale_items WHERE product_id = $product_id")->fetch_assoc()['count'];
        $check_purchases = $conn->query("SELECT COUNT(*) as count FROM purchase_items WHERE product_id = $product_id")->fetch_assoc()['count'];
        
        if ($check_sales > 0 || $check_purchases > 0) {
            // إلغاء تفعيل المنتج بدلاً من حذفه
            $update_query = "UPDATE products SET is_active = 0 WHERE id = ?";
            $stmt = $conn->prepare($update_query);
            $stmt->bind_param("i", $product_id);
            $stmt->execute();
            $_SESSION['success_message'] = "تم إلغاء تفعيل المنتج بنجاح (لا يمكن حذفه لوجود معاملات مرتبطة به)";
        } else {
            // حذف المنتج نهائياً
            $delete_query = "DELETE FROM products WHERE id = ?";
            $stmt = $conn->prepare($delete_query);
            $stmt->bind_param("i", $product_id);
            $stmt->execute();
            $_SESSION['success_message'] = "تم حذف المنتج بنجاح";
        }
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = "خطأ في حذف المنتج: " . $e->getMessage();
    }
    
    header("Location: index.php");
    exit();
}

// الحصول على المنتجات مع الفلترة والبحث
$search = isset($_GET['search']) ? clean($conn, $_GET['search']) : '';
$category_id = isset($_GET['category_id']) ? clean($conn, $_GET['category_id']) : '';
$status = isset($_GET['status']) ? clean($conn, $_GET['status']) : '';
$stock_status = isset($_GET['stock_status']) ? clean($conn, $_GET['stock_status']) : '';

$where_conditions = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_conditions[] = "(p.name LIKE ? OR p.barcode LIKE ? OR p.description LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= 'sss';
}

if (!empty($category_id)) {
    $where_conditions[] = "p.category_id = ?";
    $params[] = $category_id;
    $types .= 'i';
}

if ($status !== '') {
    $where_conditions[] = "p.is_active = ?";
    $params[] = $status;
    $types .= 'i';
}

if (!empty($stock_status)) {
    if ($stock_status == 'low') {
        $where_conditions[] = "p.stock_quantity <= p.min_stock";
    } elseif ($stock_status == 'out') {
        $where_conditions[] = "p.stock_quantity <= 0";
    } elseif ($stock_status == 'available') {
        $where_conditions[] = "p.stock_quantity > p.min_stock";
    }
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
}

// استعلام المنتجات
$query = "SELECT p.*, c.name as category_name 
          FROM products p 
          LEFT JOIN categories c ON p.category_id = c.id 
          $where_clause 
          ORDER BY p.name ASC";

if (!empty($params)) {
    $stmt = $conn->prepare($query);
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $products_result = $stmt->get_result();
} else {
    $products_result = $conn->query($query);
}

// الحصول على قائمة الفئات للفلتر
$categories_query = "SELECT id, name FROM categories ORDER BY name";
$categories_result = $conn->query($categories_query);

// حساب الإحصائيات
$stats_query = "SELECT 
                COUNT(*) as total_products,
                COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_products,
                COUNT(CASE WHEN stock_quantity <= 0 THEN 1 END) as out_of_stock,
                COUNT(CASE WHEN stock_quantity <= min_stock THEN 1 END) as low_stock,
                COALESCE(SUM(stock_quantity * cost_price), 0) as total_value
                FROM products p $where_clause";

if (!empty($params)) {
    $stmt = $conn->prepare($stats_query);
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $stats = $stmt->get_result()->fetch_assoc();
} else {
    $stats = $conn->query($stats_query)->fetch_assoc();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .btn-action {
            padding: 0.25rem 0.5rem;
            margin: 0 0.1rem;
            border-radius: 0.375rem;
        }
        .search-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .product-image {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 8px;
        }
        .stock-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .stock-available {
            background-color: #d4edda;
            color: #155724;
        }
        .stock-low {
            background-color: #fff3cd;
            color: #856404;
        }
        .stock-out {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">إدارة وعرض جميع المنتجات والمخزون</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="add.php" class="btn btn-light btn-lg">
                        <i class="fas fa-plus me-2"></i>
                        منتج جديد
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg ms-2">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-purple mb-2">
                            <i class="fas fa-box fa-2x"></i>
                        </div>
                        <h3 class="card-title" style="color: #6f42c1;"><?php echo number_format($stats['total_products']); ?></h3>
                        <p class="card-text text-muted">إجمالي المنتجات</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-success mb-2">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                        <h3 class="card-title text-success"><?php echo number_format($stats['active_products']); ?></h3>
                        <p class="card-text text-muted">منتجات نشطة</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-warning mb-2">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                        <h3 class="card-title text-warning"><?php echo number_format($stats['low_stock']); ?></h3>
                        <p class="card-text text-muted">مخزون منخفض</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-danger mb-2">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                        <h3 class="card-title text-danger"><?php echo number_format($stats['out_of_stock']); ?></h3>
                        <p class="card-text text-muted">نفد المخزون</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-8 col-sm-12 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-info mb-2">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                        <h3 class="card-title text-info"><?php echo formatMoney($stats['total_value']); ?></h3>
                        <p class="card-text text-muted">قيمة المخزون الإجمالية</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- البحث والفلترة -->
        <div class="search-container">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="اسم المنتج، الباركود، أو الوصف">
                </div>
                <div class="col-md-2">
                    <label for="category_id" class="form-label">الفئة</label>
                    <select class="form-select" id="category_id" name="category_id">
                        <option value="">جميع الفئات</option>
                        <?php while ($category = $categories_result->fetch_assoc()): ?>
                            <option value="<?php echo $category['id']; ?>" 
                                    <?php echo ($category_id == $category['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="1" <?php echo ($status === '1') ? 'selected' : ''; ?>>نشط</option>
                        <option value="0" <?php echo ($status === '0') ? 'selected' : ''; ?>>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="stock_status" class="form-label">حالة المخزون</label>
                    <select class="form-select" id="stock_status" name="stock_status">
                        <option value="">جميع حالات المخزون</option>
                        <option value="available" <?php echo ($stock_status == 'available') ? 'selected' : ''; ?>>متوفر</option>
                        <option value="low" <?php echo ($stock_status == 'low') ? 'selected' : ''; ?>>منخفض</option>
                        <option value="out" <?php echo ($stock_status == 'out') ? 'selected' : ''; ?>>نفد</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn" style="background-color: #6f42c1; color: white;">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                    </div>
                </div>
            </form>
            
            <?php if (!empty($search) || !empty($category_id) || $status !== '' || !empty($stock_status)): ?>
                <div class="mt-3">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i> مسح الفلاتر
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- جدول المنتجات -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>الصورة</th>
                            <th>اسم المنتج</th>
                            <th>الباركود</th>
                            <th>الفئة</th>
                            <th>سعر التكلفة</th>
                            <th>سعر البيع</th>
                            <th>المخزون</th>
                            <th>الوحدة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($products_result->num_rows > 0): ?>
                            <?php while ($product = $products_result->fetch_assoc()): ?>
                                <?php 
                                $stock_class = '';
                                $stock_text = '';
                                if ($product['stock_quantity'] <= 0) {
                                    $stock_class = 'stock-out';
                                    $stock_text = 'نفد';
                                } elseif ($product['stock_quantity'] <= $product['min_stock']) {
                                    $stock_class = 'stock-low';
                                    $stock_text = 'منخفض';
                                } else {
                                    $stock_class = 'stock-available';
                                    $stock_text = 'متوفر';
                                }
                                ?>
                                <tr>
                                    <td>
                                        <?php if (!empty($product['image'])): ?>
                                            <img src="../../uploads/products/<?php echo htmlspecialchars($product['image']); ?>" 
                                                 class="product-image" alt="صورة المنتج">
                                        <?php else: ?>
                                            <div class="product-image bg-light d-flex align-items-center justify-content-center">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                        <?php if (!empty($product['description'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars(substr($product['description'], 0, 50)); ?>...</small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($product['barcode'] ?? '-'); ?></td>
                                    <td><?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?></td>
                                    <td><?php echo formatMoney($product['cost_price']); ?></td>
                                    <td><?php echo formatMoney($product['selling_price']); ?></td>
                                    <td>
                                        <span class="stock-badge <?php echo $stock_class; ?>">
                                            <?php echo number_format($product['stock_quantity'], 3); ?> - <?php echo $stock_text; ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($product['unit'] ?? 'قطعة'); ?></td>
                                    <td>
                                        <?php if ($product['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="view.php?id=<?php echo $product['id']; ?>" 
                                               class="btn btn-info btn-action" 
                                               data-bs-toggle="tooltip" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $product['id']; ?>" 
                                               class="btn btn-warning btn-action" 
                                               data-bs-toggle="tooltip" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-danger btn-action" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#deleteModal<?php echo $product['id']; ?>"
                                                    title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Modal حذف المنتج -->
                                        <div class="modal fade" id="deleteModal<?php echo $product['id']; ?>" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">تأكيد الحذف</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>هل أنت متأكد من حذف المنتج <strong><?php echo htmlspecialchars($product['name']); ?></strong>؟</p>
                                                        <p class="text-warning"><small>ملاحظة: إذا كان للمنتج معاملات مرتبطة به، سيتم إلغاء تفعيله بدلاً من حذفه.</small></p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                                            <button type="submit" name="delete_product" class="btn btn-danger">حذف</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد منتجات</h5>
                                    <p class="text-muted">لم يتم العثور على أي منتجات تطابق معايير البحث</p>
                                    <a href="add.php" class="btn" style="background-color: #6f42c1; color: white;">
                                        <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                                    </a>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تفعيل التلميحات
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    </script>

</body>
</html>
