<?php
/**
 * صفحة عرض تفاصيل المنتج
 * View Product Details Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

// التحقق من وجود معرف المنتج
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error_message'] = "معرف المنتج غير صحيح";
    header("Location: index.php");
    exit();
}

$product_id = intval($_GET['id']);

// الحصول على بيانات المنتج
$product_query = "SELECT p.*, c.name as category_name 
                  FROM products p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  WHERE p.id = ?";

$stmt = $conn->prepare($product_query);
$stmt->bind_param("i", $product_id);
$stmt->execute();
$product_result = $stmt->get_result();

if ($product_result->num_rows === 0) {
    $_SESSION['error_message'] = "المنتج غير موجود";
    header("Location: index.php");
    exit();
}

$product = $product_result->fetch_assoc();

// الحصول على إحصائيات المنتج
$sales_stats_query = "SELECT 
                      COUNT(*) as sales_count,
                      COALESCE(SUM(quantity), 0) as total_sold,
                      COALESCE(SUM(total_price), 0) as total_revenue
                      FROM sale_items WHERE product_id = ?";
$stmt = $conn->prepare($sales_stats_query);
$stmt->bind_param("i", $product_id);
$stmt->execute();
$sales_stats = $stmt->get_result()->fetch_assoc();

$purchases_stats_query = "SELECT 
                         COUNT(*) as purchases_count,
                         COALESCE(SUM(quantity), 0) as total_purchased,
                         COALESCE(SUM(total_price), 0) as total_cost
                         FROM purchase_items WHERE product_id = ?";
$stmt = $conn->prepare($purchases_stats_query);
$stmt->bind_param("i", $product_id);
$stmt->execute();
$purchases_stats = $stmt->get_result()->fetch_assoc();

// حساب الربح المتوقع
$profit_per_unit = $product['selling_price'] - $product['cost_price'];
$total_profit_potential = $product['stock_quantity'] * $profit_per_unit;

$page_title = "تفاصيل المنتج: " . $product['name'];
$page_icon = "fas fa-eye";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .info-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .stats-card {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
        }
        .product-image {
            width: 100%;
            max-width: 300px;
            height: 300px;
            object-fit: cover;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
        }
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        .stock-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        تفاصيل المنتج
                    </h1>
                    <p class="mb-0 mt-2"><?php echo htmlspecialchars($product['name']); ?></p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="edit.php?id=<?php echo $product['id']; ?>" class="btn btn-warning btn-lg me-2">
                        <i class="fas fa-edit me-2"></i>تعديل
                    </a>
                    <a href="index.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-list me-2"></i>قائمة المنتجات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            
            <!-- معلومات المنتج -->
            <div class="col-lg-8">
                
                <!-- المعلومات الأساسية -->
                <div class="info-card">
                    <h4 class="mb-4">
                        <i class="fas fa-info-circle me-2" style="color: #6f42c1;"></i>
                        المعلومات الأساسية
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>اسم المنتج:</strong></td>
                                    <td><?php echo htmlspecialchars($product['name']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>الباركود:</strong></td>
                                    <td><?php echo htmlspecialchars($product['barcode'] ?? 'غير محدد'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>الفئة:</strong></td>
                                    <td><?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>الوحدة:</strong></td>
                                    <td><?php echo htmlspecialchars($product['unit'] ?? 'قطعة'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>الحالة:</strong></td>
                                    <td>
                                        <?php if ($product['is_active']): ?>
                                            <span class="status-badge status-active">نشط</span>
                                        <?php else: ?>
                                            <span class="status-badge status-inactive">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($product['created_at'])); ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-4 text-center">
                            <?php if (!empty($product['image'])): ?>
                                <img src="../../uploads/products/<?php echo htmlspecialchars($product['image']); ?>" 
                                     class="product-image" alt="صورة المنتج">
                            <?php else: ?>
                                <div class="product-image bg-light d-flex align-items-center justify-content-center">
                                    <i class="fas fa-image fa-4x text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <?php if (!empty($product['description'])): ?>
                        <div class="mt-3">
                            <strong>الوصف:</strong>
                            <p class="mt-2 p-3 bg-light rounded"><?php echo nl2br(htmlspecialchars($product['description'])); ?></p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- الأسعار والمخزون -->
                <div class="info-card">
                    <h4 class="mb-4">
                        <i class="fas fa-money-bill-wave me-2" style="color: #6f42c1;"></i>
                        الأسعار والمخزون
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>سعر التكلفة:</strong></td>
                                    <td><?php echo formatMoney($product['cost_price']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>سعر البيع:</strong></td>
                                    <td><?php echo formatMoney($product['selling_price']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>هامش الربح:</strong></td>
                                    <td>
                                        <?php 
                                        $profit_margin = $product['cost_price'] > 0 ? (($profit_per_unit / $product['cost_price']) * 100) : 0;
                                        echo number_format($profit_margin, 2) . '%';
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>الربح لكل وحدة:</strong></td>
                                    <td class="<?php echo ($profit_per_unit >= 0) ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo formatMoney($profit_per_unit); ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="stock-info">
                                <h5>معلومات المخزون</h5>
                                <div class="row mt-3">
                                    <div class="col-6">
                                        <h3><?php echo number_format($product['stock_quantity'], 3); ?></h3>
                                        <p class="mb-0">الكمية الحالية</p>
                                    </div>
                                    <div class="col-6">
                                        <h3><?php echo number_format($product['min_stock'], 3); ?></h3>
                                        <p class="mb-0">الحد الأدنى</p>
                                    </div>
                                </div>
                                <?php if ($product['stock_quantity'] <= $product['min_stock']): ?>
                                    <div class="alert alert-warning mt-3 mb-0">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        المخزون منخفض!
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات المبيعات والمشتريات -->
                <div class="info-card">
                    <h4 class="mb-4">
                        <i class="fas fa-chart-bar me-2" style="color: #6f42c1;"></i>
                        إحصائيات المعاملات
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-success">المبيعات</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td>عدد المبيعات:</td>
                                    <td><strong><?php echo number_format($sales_stats['sales_count']); ?></strong></td>
                                </tr>
                                <tr>
                                    <td>إجمالي الكمية المباعة:</td>
                                    <td><strong><?php echo number_format($sales_stats['total_sold'], 3); ?></strong></td>
                                </tr>
                                <tr>
                                    <td>إجمالي الإيرادات:</td>
                                    <td><strong><?php echo formatMoney($sales_stats['total_revenue']); ?></strong></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-primary">المشتريات</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td>عدد المشتريات:</td>
                                    <td><strong><?php echo number_format($purchases_stats['purchases_count']); ?></strong></td>
                                </tr>
                                <tr>
                                    <td>إجمالي الكمية المشتراة:</td>
                                    <td><strong><?php echo number_format($purchases_stats['total_purchased'], 3); ?></strong></td>
                                </tr>
                                <tr>
                                    <td>إجمالي التكلفة:</td>
                                    <td><strong><?php echo formatMoney($purchases_stats['total_cost']); ?></strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                
            </div>

            <!-- الملخص والإجراءات -->
            <div class="col-lg-4">
                <div class="stats-card">
                    <h4 class="mb-4">
                        <i class="fas fa-calculator me-2"></i>
                        ملخص المنتج
                    </h4>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>قيمة المخزون الحالي:</span>
                        <strong><?php echo formatMoney($product['stock_quantity'] * $product['cost_price']); ?></strong>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>الربح المحتمل:</span>
                        <strong class="<?php echo ($total_profit_potential >= 0) ? 'text-success' : 'text-danger'; ?>">
                            <?php echo formatMoney($total_profit_potential); ?>
                        </strong>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-4">
                        <span>معدل دوران المخزون:</span>
                        <strong>
                            <?php 
                            $turnover_rate = $product['stock_quantity'] > 0 ? ($sales_stats['total_sold'] / $product['stock_quantity']) : 0;
                            echo number_format($turnover_rate, 2);
                            ?>
                        </strong>
                    </div>
                    
                    <hr style="border-color: rgba(255,255,255,0.3);">
                    
                    <div class="d-grid gap-2">
                        <a href="edit.php?id=<?php echo $product['id']; ?>" class="btn btn-light">
                            <i class="fas fa-edit me-2"></i>تعديل المنتج
                        </a>
                        <a href="index.php" class="btn btn-outline-light">
                            <i class="fas fa-list me-2"></i>العودة للقائمة
                        </a>
                    </div>
                    
                    <?php if ($product['stock_quantity'] <= $product['min_stock']): ?>
                        <div class="alert alert-warning mt-3" style="background-color: rgba(255,255,255,0.1); border-color: rgba(255,255,255,0.3); color: white;">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> المخزون منخفض، يحتاج إعادة تموين
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
