<?php
/**
 * صفحة طباعة مستند المشتريات
 * Print Purchase Document Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

// التحقق من وجود معرف المشتريات
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo "معرف المشتريات غير صحيح";
    exit();
}

$purchase_id = intval($_GET['id']);

// الحصول على بيانات المشتريات
$purchase_query = "SELECT p.*, s.name as supplier_name, s.company as supplier_company, s.phone as supplier_phone, s.address as supplier_address, 
                   u.name as user_name 
                   FROM purchases p 
                   LEFT JOIN suppliers s ON p.supplier_id = s.id 
                   LEFT JOIN users u ON p.user_id = u.id 
                   WHERE p.id = ?";

$stmt = $conn->prepare($purchase_query);
$stmt->bind_param("i", $purchase_id);
$stmt->execute();
$purchase_result = $stmt->get_result();

if ($purchase_result->num_rows === 0) {
    echo "المشتريات غير موجودة";
    exit();
}

$purchase = $purchase_result->fetch_assoc();

// الحصول على أصناف المشتريات
$items_query = "SELECT pi.*, p.name as product_name, p.unit 
                FROM purchase_items pi 
                LEFT JOIN products p ON pi.product_id = p.id 
                WHERE pi.purchase_id = ? 
                ORDER BY pi.id";

$stmt = $conn->prepare($items_query);
$stmt->bind_param("i", $purchase_id);
$stmt->execute();
$items_result = $stmt->get_result();

// الحصول على إعدادات المتجر
$store_name = getSetting('store_name', 'متجر Zero');
$store_address = getSetting('store_address', '');
$store_phone = getSetting('store_phone', '');
$store_currency = getSetting('store_currency', 'ريال');
$store_logo = getSetting('store_logo', '');

// حساب المبلغ المتبقي
$remaining_amount = $purchase['final_amount'] - $purchase['paid_amount'];

// الحصول على رصيد المورد الحالي إذا كان موجوداً
$supplier_balance = 0;
if (!empty($purchase['supplier_id'])) {
    $balance_query = "SELECT balance FROM suppliers WHERE id = ?";
    $balance_stmt = $conn->prepare($balance_query);
    $balance_stmt->bind_param("i", $purchase['supplier_id']);
    $balance_stmt->execute();
    $balance_result = $balance_stmt->get_result();
    if ($balance_result->num_rows > 0) {
        $supplier_balance = $balance_result->fetch_assoc()['balance'];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستند مشتريات رقم: <?php echo htmlspecialchars($purchase['purchase_number']); ?></title>
    
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
            color: #333;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .document-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .document-header {
            padding: 30px;
            border-bottom: 3px solid #28a745;
            background: #f8f9fa;
        }

        .header-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .store-info {
            flex: 1;
            text-align: right;
            padding-right: 20px;
        }

        .store-name {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .store-details {
            font-size: 12px;
            color: #666;
            line-height: 1.6;
        }

        .logo-section {
            flex: 1;
            text-align: center;
            padding: 0 20px;
        }

        .store-logo {
            max-width: 120px;
            max-height: 80px;
            border-radius: 5px;
        }

        .logo-placeholder {
            width: 120px;
            height: 80px;
            border: 2px dashed #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
            margin: 0 auto;
        }

        .document-info {
            flex: 1;
            text-align: left;
            padding-left: 20px;
        }

        .document-title {
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 10px;
        }

        .document-meta {
            font-size: 12px;
            color: #666;
            line-height: 1.6;
        }

        .document-details {
            padding: 30px;
        }

        .supplier-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .supplier-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #28a745;
            padding-bottom: 5px;
        }

        .supplier-details {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .supplier-basic {
            flex: 1;
        }

        .supplier-balance {
            flex: 1;
            text-align: left;
            padding-left: 20px;
        }

        .balance-info {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        .balance-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .balance-amount {
            font-size: 18px;
            font-weight: bold;
        }

        .balance-credit {
            color: #28a745;
        }

        .balance-debit {
            color: #dc3545;
        }

        .balance-zero {
            color: #6c757d;
        }
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .items-table th {
            background: #28a745;
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
        }
        
        .items-table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .summary-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        
        .summary-row.total {
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
            border-top: 2px solid #28a745;
            padding-top: 15px;
            margin-top: 15px;
        }
        
        .payment-status {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .status-paid {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-partial {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-unpaid {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .document-footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            color: #666;
        }
        
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .signature-box {
            text-align: center;
            width: 200px;
        }
        
        .signature-line {
            border-bottom: 1px solid #333;
            margin-bottom: 10px;
            height: 40px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .document-container {
                border: none;
                border-radius: 0;
                box-shadow: none;
            }
            
            .no-print {
                display: none !important;
            }
        }
        
        @media (max-width: 768px) {
            .document-meta {
                flex-direction: column;
                gap: 20px;
            }
            
            .items-table {
                font-size: 12px;
            }
            
            .items-table th,
            .items-table td {
                padding: 8px 5px;
            }
        }
    </style>
</head>
<body>
    
    <div class="document-container">
        
        <!-- رأس المستند -->
        <div class="document-header">
            <div class="header-row">
                <!-- بيانات المحل -->
                <div class="store-info">
                    <div class="store-name"><?php echo htmlspecialchars($store_name); ?></div>
                    <div class="store-details">
                        <?php if (!empty($store_address)): ?>
                            <div><?php echo htmlspecialchars($store_address); ?></div>
                        <?php endif; ?>
                        <?php if (!empty($store_phone)): ?>
                            <div>هاتف: <?php echo htmlspecialchars($store_phone); ?></div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- الشعار -->
                <div class="logo-section">
                    <?php if (!empty($store_logo) && file_exists("../../" . $store_logo)): ?>
                        <img src="../../<?php echo $store_logo; ?>" alt="شعار المحل" class="store-logo">
                    <?php else: ?>
                        <div class="logo-placeholder">
                            شعار المحل
                        </div>
                    <?php endif; ?>
                </div>

                <!-- بيانات المستند -->
                <div class="document-info">
                    <div class="document-title">مستند مشتريات</div>
                    <div class="document-meta">
                        <div><strong>رقم المستند:</strong> <?php echo $purchase['id']; ?></div>
                        <div><strong>التاريخ:</strong> <?php echo date('Y-m-d', strtotime($purchase['purchase_date'])); ?></div>
                        <div><strong>الوقت:</strong> <?php echo date('H:i', strtotime($purchase['created_at'])); ?></div>
                        <div><strong>المستخدم:</strong> <?php echo htmlspecialchars($purchase['user_name']); ?></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل المستند -->
        <div class="document-details">
            
            <!-- جدول الأصناف -->
            <table class="items-table">
                <thead>
                    <tr>
                        <th>م</th>
                        <th>المنتج</th>
                        <th>الكمية</th>
                        <th>الوحدة</th>
                        <th>سعر الوحدة</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($items_result->num_rows > 0): ?>
                        <?php $counter = 1; ?>
                        <?php while ($item = $items_result->fetch_assoc()): ?>
                            <tr>
                                <td><?php echo $counter++; ?></td>
                                <td style="text-align: right;"><?php echo htmlspecialchars($item['product_name']); ?></td>
                                <td><?php echo number_format($item['quantity'], 3); ?></td>
                                <td><?php echo htmlspecialchars($item['unit'] ?? 'قطعة'); ?></td>
                                <td><?php echo number_format($item['unit_price'], 2); ?> <?php echo $store_currency; ?></td>
                                <td><?php echo number_format($item['total_price'], 2); ?> <?php echo $store_currency; ?></td>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" style="text-align: center; color: #666;">لا توجد أصناف</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
            
            <!-- ملخص المبالغ -->
            <div class="summary-section">
                <div class="summary-row">
                    <span>المبلغ الإجمالي:</span>
                    <span><?php echo number_format($purchase['total_amount'], 2); ?> <?php echo $store_currency; ?></span>
                </div>
                
                <?php if ($purchase['discount_amount'] > 0): ?>
                    <div class="summary-row">
                        <span>مبلغ الخصم:</span>
                        <span style="color: #dc3545;">- <?php echo number_format($purchase['discount_amount'], 2); ?> <?php echo $store_currency; ?></span>
                    </div>
                <?php endif; ?>
                
                <div class="summary-row total">
                    <span>المبلغ النهائي:</span>
                    <span><?php echo number_format($purchase['final_amount'], 2); ?> <?php echo $store_currency; ?></span>
                </div>
                
                <div class="summary-row">
                    <span>المبلغ المدفوع:</span>
                    <span style="color: #28a745;"><?php echo number_format($purchase['paid_amount'], 2); ?> <?php echo $store_currency; ?></span>
                </div>
                
                <?php if ($remaining_amount > 0): ?>
                    <div class="summary-row">
                        <span>المبلغ المتبقي:</span>
                        <span style="color: #ffc107;"><?php echo number_format($remaining_amount, 2); ?> <?php echo $store_currency; ?></span>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- حالة الدفع -->
            <div class="payment-status <?php 
                if ($remaining_amount <= 0) echo 'status-paid';
                elseif ($purchase['paid_amount'] > 0) echo 'status-partial';
                else echo 'status-unpaid';
            ?>">
                <?php if ($remaining_amount <= 0): ?>
                    ✅ تم الدفع بالكامل
                <?php elseif ($purchase['paid_amount'] > 0): ?>
                    ⚠️ دفع جزئي - المتبقي: <?php echo number_format($remaining_amount, 2); ?> <?php echo $store_currency; ?>
                <?php else: ?>
                    ❌ لم يتم الدفع
                <?php endif; ?>
            </div>
            
            <!-- طريقة الدفع -->
            <div style="text-align: center; margin-bottom: 20px;">
                <strong>طريقة الدفع:</strong>
                <?php
                $payment_methods = [
                    'cash' => 'نقدي',
                    'card' => 'بطاقة',
                    'bank_transfer' => 'تحويل بنكي'
                ];
                echo $payment_methods[$purchase['payment_method']] ?? $purchase['payment_method'];
                ?>
            </div>
            
            <!-- ملاحظات -->
            <?php if (!empty($purchase['notes'])): ?>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <strong>ملاحظات:</strong><br>
                    <?php echo nl2br(htmlspecialchars($purchase['notes'])); ?>
                </div>
            <?php endif; ?>
            
            <!-- معلومات المورد وكشف الحساب -->
            <?php if (!empty($purchase['supplier_id'])): ?>
                <div class="supplier-info">
                    <div class="supplier-title">معلومات المورد وكشف الحساب</div>
                    <div class="supplier-details">
                        <div class="supplier-basic">
                            <div><strong>اسم المورد:</strong> <?php echo htmlspecialchars($purchase['supplier_name']); ?></div>
                            <?php if (!empty($purchase['supplier_company'])): ?>
                                <div><strong>الشركة:</strong> <?php echo htmlspecialchars($purchase['supplier_company']); ?></div>
                            <?php endif; ?>
                            <?php if (!empty($purchase['supplier_phone'])): ?>
                                <div><strong>رقم الهاتف:</strong> <?php echo htmlspecialchars($purchase['supplier_phone']); ?></div>
                            <?php endif; ?>
                            <?php if (!empty($purchase['supplier_address'])): ?>
                                <div><strong>العنوان:</strong> <?php echo htmlspecialchars($purchase['supplier_address']); ?></div>
                            <?php endif; ?>
                        </div>
                        <div class="supplier-balance">
                            <div class="balance-info">
                                <div class="balance-title">كشف حساب المورد</div>
                                <div class="balance-amount <?php
                                    if ($supplier_balance > 0) echo 'balance-credit';
                                    elseif ($supplier_balance < 0) echo 'balance-debit';
                                    else echo 'balance-zero';
                                ?>">
                                    <?php
                                    if ($supplier_balance > 0) {
                                        echo "دائن: " . number_format($supplier_balance, 2) . " " . $store_currency;
                                    } elseif ($supplier_balance < 0) {
                                        echo "مدين: " . number_format(abs($supplier_balance), 2) . " " . $store_currency;
                                    } else {
                                        echo "متعادل: 0.00 " . $store_currency;
                                    }
                                    ?>
                                </div>
                                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                                    <?php
                                    if ($supplier_balance > 0) {
                                        echo "المورد له رصيد لدى المحل";
                                    } elseif ($supplier_balance < 0) {
                                        echo "المورد عليه مبلغ للمحل";
                                    } else {
                                        echo "لا يوجد رصيد مستحق";
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- التوقيعات -->
            <div class="signature-section">
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div>توقيع المورد</div>
                </div>
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div>توقيع المستلم</div>
                </div>
            </div>
            
        </div>
        
        <!-- تذييل المستند -->
        <div class="document-footer">
            <div>شكراً لتعاملكم معنا</div>
            <div style="margin-top: 10px; font-size: 12px; color: #999;">
                تم الطباعة في: <?php echo date('Y-m-d H:i:s'); ?>
            </div>
        </div>
        
    </div>
    
    <!-- أزرار التحكم -->
    <div class="no-print" style="text-align: center; margin: 20px 0;">
        <button onclick="window.print()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 0 10px; cursor: pointer;">
            🖨️ طباعة
        </button>
        <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 0 10px; cursor: pointer;">
            ❌ إغلاق
        </button>
        <a href="view.php?id=<?php echo $purchase['id']; ?>" style="background: #007bff; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; margin: 0 10px; display: inline-block;">
            👁️ عرض التفاصيل
        </a>
    </div>
    
    <script>
        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        // window.onload = function() { window.print(); }
    </script>

</body>
</html>
