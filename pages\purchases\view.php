<?php
/**
 * صفحة عرض تفاصيل المشتريات
 * View Purchase Details Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

// التحقق من وجود معرف المشتريات
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error_message'] = "معرف المشتريات غير صحيح";
    header("Location: index.php");
    exit();
}

$purchase_id = intval($_GET['id']);

// الحصول على بيانات المشتريات
$purchase_query = "SELECT p.*, s.name as supplier_name, s.company as supplier_company, s.phone as supplier_phone, s.address as supplier_address, 
                   u.name as user_name 
                   FROM purchases p 
                   LEFT JOIN suppliers s ON p.supplier_id = s.id 
                   LEFT JOIN users u ON p.user_id = u.id 
                   WHERE p.id = ?";

$stmt = $conn->prepare($purchase_query);
$stmt->bind_param("i", $purchase_id);
$stmt->execute();
$purchase_result = $stmt->get_result();

if ($purchase_result->num_rows === 0) {
    $_SESSION['error_message'] = "المشتريات غير موجودة";
    header("Location: index.php");
    exit();
}

$purchase = $purchase_result->fetch_assoc();

// الحصول على أصناف المشتريات
$items_query = "SELECT pi.*, p.name as product_name, p.unit 
                FROM purchase_items pi 
                LEFT JOIN products p ON pi.product_id = p.id 
                WHERE pi.purchase_id = ? 
                ORDER BY pi.id";

$stmt = $conn->prepare($items_query);
$stmt->bind_param("i", $purchase_id);
$stmt->execute();
$items_result = $stmt->get_result();

$page_title = "تفاصيل المشتريات رقم: " . $purchase['purchase_number'];
$page_icon = "fas fa-eye";

// حساب المبلغ المتبقي
$remaining_amount = $purchase['final_amount'] - $purchase['paid_amount'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .info-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .summary-card {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
        }
        .items-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
        }
        .status-paid {
            background-color: #d4edda;
            color: #155724;
        }
        .status-partial {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-unpaid {
            background-color: #f8d7da;
            color: #721c24;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            .main-header {
                background: #333 !important;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header no-print">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        تفاصيل المشتريات
                    </h1>
                    <p class="mb-0 mt-2">رقم المشتريات: <?php echo htmlspecialchars($purchase['purchase_number']); ?></p>
                </div>
                <div class="col-md-6 text-end">
                    <button onclick="window.print()" class="btn btn-light btn-lg me-2">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    <a href="edit.php?id=<?php echo $purchase['id']; ?>" class="btn btn-warning btn-lg me-2">
                        <i class="fas fa-edit me-2"></i>
                        تعديل
                    </a>
                    <a href="index.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-list me-2"></i>
                        قائمة المشتريات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show no-print" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            
            <!-- معلومات المشتريات -->
            <div class="col-lg-8">
                
                <!-- معلومات أساسية -->
                <div class="info-card">
                    <h4 class="mb-4">
                        <i class="fas fa-info-circle me-2 text-success"></i>
                        معلومات المشتريات
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>رقم المشتريات:</strong></td>
                                    <td><?php echo htmlspecialchars($purchase['purchase_number']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ المشتريات:</strong></td>
                                    <td><?php echo date('Y-m-d', strtotime($purchase['purchase_date'])); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>المورد:</strong></td>
                                    <td><?php echo htmlspecialchars($purchase['supplier_name'] ?? 'غير محدد'); ?></td>
                                </tr>
                                <?php if (!empty($purchase['supplier_company'])): ?>
                                <tr>
                                    <td><strong>الشركة:</strong></td>
                                    <td><?php echo htmlspecialchars($purchase['supplier_company']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if (!empty($purchase['supplier_phone'])): ?>
                                <tr>
                                    <td><strong>هاتف المورد:</strong></td>
                                    <td><?php echo htmlspecialchars($purchase['supplier_phone']); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>طريقة الدفع:</strong></td>
                                    <td>
                                        <?php
                                        $payment_methods = [
                                            'cash' => 'نقدي',
                                            'card' => 'بطاقة',
                                            'bank_transfer' => 'تحويل بنكي'
                                        ];
                                        echo $payment_methods[$purchase['payment_method']] ?? $purchase['payment_method'];
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>المستخدم:</strong></td>
                                    <td><?php echo htmlspecialchars($purchase['user_name']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ الإنشاء:</strong></td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($purchase['created_at'])); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>حالة الدفع:</strong></td>
                                    <td>
                                        <?php if ($remaining_amount <= 0): ?>
                                            <span class="status-badge status-paid">مدفوع بالكامل</span>
                                        <?php elseif ($purchase['paid_amount'] > 0): ?>
                                            <span class="status-badge status-partial">مدفوع جزئياً</span>
                                        <?php else: ?>
                                            <span class="status-badge status-unpaid">غير مدفوع</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <?php if (!empty($purchase['notes'])): ?>
                        <div class="mt-3">
                            <strong>ملاحظات:</strong>
                            <p class="mt-2 p-3 bg-light rounded"><?php echo nl2br(htmlspecialchars($purchase['notes'])); ?></p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- أصناف المشتريات -->
                <div class="items-table">
                    <div class="p-3 bg-success text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-box me-2"></i>
                            أصناف المشتريات
                        </h4>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>الوحدة</th>
                                    <th>سعر الوحدة</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($items_result->num_rows > 0): ?>
                                    <?php while ($item = $items_result->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                            <td><?php echo number_format($item['quantity'], 3); ?></td>
                                            <td><?php echo htmlspecialchars($item['unit'] ?? 'قطعة'); ?></td>
                                            <td><?php echo formatMoney($item['unit_price']); ?></td>
                                            <td class="fw-bold"><?php echo formatMoney($item['total_price']); ?></td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5" class="text-center py-4">
                                            <i class="fas fa-box-open fa-2x text-muted mb-2"></i>
                                            <p class="text-muted">لا توجد أصناف في هذه المشتريات</p>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
            </div>

            <!-- ملخص المشتريات -->
            <div class="col-lg-4">
                <div class="summary-card">
                    <h4 class="mb-4">
                        <i class="fas fa-calculator me-2"></i>
                        ملخص المشتريات
                    </h4>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>المبلغ الإجمالي:</span>
                        <strong><?php echo formatMoney($purchase['total_amount']); ?></strong>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>مبلغ الخصم:</span>
                        <strong class="text-warning">- <?php echo formatMoney($purchase['discount_amount']); ?></strong>
                    </div>
                    
                    <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>المبلغ النهائي:</span>
                        <strong class="fs-5"><?php echo formatMoney($purchase['final_amount']); ?></strong>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-3">
                        <span>المبلغ المدفوع:</span>
                        <strong class="text-success"><?php echo formatMoney($purchase['paid_amount']); ?></strong>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-4">
                        <span>المبلغ المتبقي:</span>
                        <strong class="<?php echo ($remaining_amount > 0) ? 'text-warning' : 'text-success'; ?>">
                            <?php echo formatMoney($remaining_amount); ?>
                        </strong>
                    </div>
                    
                    <?php if ($remaining_amount > 0): ?>
                        <div class="alert alert-warning" style="background-color: rgba(255,255,255,0.1); border-color: rgba(255,255,255,0.3); color: white;">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> يوجد مبلغ متبقي على المورد
                        </div>
                    <?php endif; ?>
                    
                    <div class="d-grid gap-2 no-print">
                        <a href="print.php?id=<?php echo $purchase['id']; ?>" class="btn btn-light" target="_blank">
                            <i class="fas fa-print me-2"></i>
                            طباعة المستند
                        </a>
                        <a href="edit.php?id=<?php echo $purchase['id']; ?>" class="btn btn-outline-light">
                            <i class="fas fa-edit me-2"></i>
                            تعديل المشتريات
                        </a>
                        <a href="index.php" class="btn btn-outline-light">
                            <i class="fas fa-list me-2"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>
            </div>
            
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
