<?php
/**
 * صفحة إضافة مبيعة جديدة مع البحث المتطور والباركود
 * Advanced Sales Add Page with Search and Barcode
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "فاتورة مبيعات متطورة";
$page_icon = "fas fa-shopping-cart";

// معالجة إضافة المبيعة (نفس الكود الموجود في add.php)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_sale'])) {
    try {
        $conn->begin_transaction();
        
        // بيانات المبيعة
        $customer_id = clean($conn, $_POST['customer_id']);
        $sale_date = clean($conn, $_POST['sale_date']);
        $total_amount = floatval($_POST['total_amount']);
        $discount_amount = floatval($_POST['discount_amount']);
        $final_amount = floatval($_POST['final_amount']);
        $paid_amount = floatval($_POST['paid_amount']);
        $payment_method = clean($conn, $_POST['payment_method']);
        $notes = clean($conn, $_POST['notes']);
        
        // توليد رقم المبيعة
        $sale_number = generateSaleNumber($conn);
        
        // إدراج المبيعة
        $insert_query = "INSERT INTO sales (sale_number, customer_id, user_id, sale_date, total_amount, discount_amount, final_amount, paid_amount, payment_method, notes) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param("siisddddss", $sale_number, $customer_id, $_SESSION['user_id'], $sale_date, $total_amount, $discount_amount, $final_amount, $paid_amount, $payment_method, $notes);
        $stmt->execute();
        
        $sale_id = $conn->insert_id;
        
        // إدراج أصناف المبيعة
        if (isset($_POST['products']) && is_array($_POST['products'])) {
            foreach ($_POST['products'] as $product_data) {
                if (!empty($product_data['product_id']) && isset($product_data['quantity']) && $product_data['quantity'] > 0) {
                    $product_id = intval($product_data['product_id']);
                    $quantity = floatval($product_data['quantity']);
                    $unit_price = floatval($product_data['unit_price']);
                    $total_price = $quantity * $unit_price;

                    // إدراج صنف المبيعة
                    $item_query = "INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)";
                    $item_stmt = $conn->prepare($item_query);
                    $item_stmt->bind_param("iiddd", $sale_id, $product_id, $quantity, $unit_price, $total_price);
                    $item_stmt->execute();

                    // تحديث المخزون
                    $update_stock = "UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?";
                    $stock_stmt = $conn->prepare($update_stock);
                    $stock_stmt->bind_param("di", $quantity, $product_id);
                    $stock_stmt->execute();
                }
            }
        }
        
        // تحديث الخزينة (إضافة للخزينة)
        if ($paid_amount > 0) {
            $description = "مبيعة رقم: $sale_number";
            if (!addTreasuryTransaction('sales', $sale_id, $paid_amount, $description, $_SESSION['user_id'], $sale_date)) {
                throw new Exception("خطأ في تحديث الخزينة");
            }
        }
        
        // تحديث رصيد العميل (إذا كان هناك مبلغ متبقي)
        $remaining_amount = $final_amount - $paid_amount;
        if ($remaining_amount > 0 && $customer_id > 1) {
            $update_customer = "UPDATE customers SET balance = balance + ? WHERE id = ?";
            $customer_stmt = $conn->prepare($update_customer);
            $customer_stmt->bind_param("di", $remaining_amount, $customer_id);
            $customer_stmt->execute();
        }
        
        $conn->commit();
        $_SESSION['success_message'] = "تم إضافة المبيعة بنجاح برقم: $sale_number";
        header("Location: view.php?id=$sale_id");
        exit();
        
    } catch (Exception $e) {
        $conn->rollback();
        $_SESSION['error_message'] = "خطأ في إضافة المبيعة: " . $e->getMessage();
    }
}

// الحصول على العملاء
$customers_query = "SELECT id, name, phone, balance FROM customers ORDER BY name";
$customers_result = $conn->query($customers_query);

// الحصول على المنتجات مع معلومات إضافية
$products_query = "SELECT p.*, c.name as category_name 
                   FROM products p 
                   LEFT JOIN categories c ON p.category_id = c.id 
                   WHERE p.stock_quantity > 0 
                   ORDER BY p.name";
$products_result = $conn->query($products_query);
$products = [];
while ($product = $products_result->fetch_assoc()) {
    $products[] = $product;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <!-- Select2 for advanced search -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .form-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .products-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
        }
        .btn-add-product {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 0.5rem 1rem;
        }
        .required {
            color: #dc3545;
        }
        .barcode-scanner {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .product-search-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .quick-add-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        .quick-add-btn {
            background: #e9ecef;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .quick-add-btn:hover {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .product-info-card {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 0.75rem;
            margin-top: 0.5rem;
        }
        .stock-warning {
            color: #dc3545;
            font-weight: bold;
        }
        .stock-good {
            color: #28a745;
            font-weight: bold;
        }
        .search-results {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-top: none;
            background: white;
            position: absolute;
            width: 100%;
            z-index: 1000;
            display: none;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .search-result-item {
            padding: 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            transition: all 0.2s ease;
        }
        .search-result-item:hover {
            background-color: #f8f9fa;
            transform: translateX(-2px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .search-result-item:last-child {
            border-bottom: none;
        }
        .search-container {
            position: relative;
        }
        .search-container input:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">فاتورة مبيعات مع بحث متطور وقارئ باركود</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="add.php" class="btn btn-light btn-lg me-2">
                        <i class="fas fa-file-invoice me-2"></i>
                        الفاتورة العادية
                    </a>
                    <a href="index.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-list me-2"></i>
                        قائمة المبيعات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <form method="POST" id="saleForm">
            <div class="row">
                <div class="col-lg-8">
                    
                    <!-- قارئ الباركود والبحث المتطور -->
                    <div class="form-container">
                        <h4 class="mb-4">
                            <i class="fas fa-search me-2 text-primary"></i>
                            البحث المتطور وقارئ الباركود
                        </h4>
                        
                        <!-- قارئ الباركود -->
                        <div class="barcode-scanner text-center">
                            <h5 class="mb-3">
                                <i class="fas fa-barcode me-2"></i>
                                قارئ الباركود
                            </h5>
                            <input type="text" class="form-control form-control-lg text-center" 
                                   id="barcodeInput" placeholder="امسح الباركود أو اكتب الكود يدوياً" 
                                   style="font-size: 1.2rem; font-weight: bold;">
                            <small class="text-white-50 mt-2 d-block">
                                <i class="fas fa-info-circle me-1"></i>
                                ضع المؤشر في هذا الحقل وامسح الباركود
                            </small>
                        </div>
                        
                        <!-- البحث المتطور -->
                        <div class="product-search-container">
                            <h6 class="mb-3">
                                <i class="fas fa-filter me-2"></i>
                                البحث المتطور في المنتجات
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <div class="search-container">
                                        <select class="form-select" id="productSearchSelect" style="width: 100%;">
                                            <option value="">ابحث عن منتج بالاسم، الباركود، أو الفئة...</option>
                                            <?php foreach ($products as $product): ?>
                                                <option value="<?php echo $product['id']; ?>"
                                                        data-barcode="<?php echo htmlspecialchars($product['barcode'] ?? ''); ?>"
                                                        data-price="<?php echo $product['selling_price']; ?>"
                                                        data-stock="<?php echo $product['stock_quantity']; ?>"
                                                        data-unit="<?php echo htmlspecialchars($product['unit'] ?? 'قطعة'); ?>"
                                                        data-category="<?php echo htmlspecialchars($product['category_name'] ?? ''); ?>">
                                                    <?php echo htmlspecialchars($product['name']); ?>
                                                    <?php if (!empty($product['barcode'])): ?>
                                                        - باركود: <?php echo htmlspecialchars($product['barcode']); ?>
                                                    <?php endif; ?>
                                                    <?php if (!empty($product['category_name'])): ?>
                                                        - فئة: <?php echo htmlspecialchars($product['category_name']); ?>
                                                    <?php endif; ?>
                                                    - مخزون: <?php echo $product['stock_quantity']; ?>
                                                    - سعر: <?php echo formatMoney($product['selling_price']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <button type="button" class="btn btn-primary w-100" onclick="addSelectedProduct()">
                                        <i class="fas fa-plus me-2"></i>
                                        إضافة للفاتورة
                                    </button>
                                </div>
                            </div>
                            
                            <!-- أزرار الإضافة السريعة للمنتجات الشائعة -->
                            <div class="quick-add-buttons" id="quickAddButtons">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- المنتجات -->
                    <div class="form-container">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4 class="mb-0">
                                <i class="fas fa-box me-2 text-primary"></i>
                                المنتجات المضافة
                            </h4>
                            <span class="badge bg-primary" id="itemsCount">0 صنف</span>
                        </div>
                        
                        <div class="products-table">
                            <table class="table table-bordered mb-0" id="productsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="30%">المنتج</th>
                                        <th width="15%">الكمية</th>
                                        <th width="15%">السعر</th>
                                        <th width="15%">الإجمالي</th>
                                        <th width="10%">الوحدة</th>
                                        <th width="10%">المخزون</th>
                                        <th width="5%">حذف</th>
                                    </tr>
                                </thead>
                                <tbody id="productsTableBody">
                                    <!-- سيتم إضافة الصفوف هنا بواسطة JavaScript -->
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <td colspan="7" class="text-center text-muted py-4" id="emptyMessage">
                                            <i class="fas fa-box-open fa-2x mb-2"></i><br>
                                            لم يتم إضافة أي منتجات بعد<br>
                                            <small>استخدم قارئ الباركود أو البحث المتطور لإضافة المنتجات</small>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- بيانات العميل -->
                    <div class="form-container">
                        <h4 class="mb-4">
                            <i class="fas fa-user me-2 text-success"></i>
                            بيانات العميل
                        </h4>
                        
                        <div class="mb-3">
                            <label for="customer_id" class="form-label">العميل <span class="required">*</span></label>
                            <select class="form-select" id="customer_id" name="customer_id" required>
                                <option value="1">عميل نقدي</option>
                                <?php while ($customer = $customers_result->fetch_assoc()): ?>
                                    <?php if ($customer['id'] != 1): ?>
                                        <option value="<?php echo $customer['id']; ?>" 
                                                data-balance="<?php echo $customer['balance']; ?>">
                                            <?php echo htmlspecialchars($customer['name']); ?>
                                            <?php if ($customer['phone']): ?>
                                                - <?php echo htmlspecialchars($customer['phone']); ?>
                                            <?php endif; ?>
                                            <?php if ($customer['balance'] != 0): ?>
                                                - رصيد: <?php echo formatMoney($customer['balance']); ?>
                                            <?php endif; ?>
                                        </option>
                                    <?php endif; ?>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="sale_date" class="form-label">تاريخ المبيعة <span class="required">*</span></label>
                            <input type="date" class="form-control" id="sale_date" name="sale_date" 
                                   value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                    </div>

                    <!-- ملخص الفاتورة -->
                    <div class="form-container">
                        <h4 class="mb-4">
                            <i class="fas fa-calculator me-2 text-warning"></i>
                            ملخص الفاتورة
                        </h4>
                        
                        <div class="mb-3">
                            <label for="total_amount" class="form-label">إجمالي المبلغ</label>
                            <input type="number" class="form-control" id="total_amount" name="total_amount" 
                                   step="0.01" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label for="discount_amount" class="form-label">الخصم</label>
                            <input type="number" class="form-control" id="discount_amount" name="discount_amount" 
                                   step="0.01" min="0" value="0" onchange="calculateTotal()">
                        </div>
                        
                        <div class="mb-3">
                            <label for="final_amount" class="form-label">المبلغ النهائي</label>
                            <input type="number" class="form-control" id="final_amount" name="final_amount" 
                                   step="0.01" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label for="paid_amount" class="form-label">المبلغ المدفوع <span class="required">*</span></label>
                            <input type="number" class="form-control" id="paid_amount" name="paid_amount" 
                                   step="0.01" min="0" required onchange="calculateRemaining()">
                        </div>
                        
                        <div class="mb-3">
                            <label for="remaining_amount" class="form-label">المبلغ المتبقي</label>
                            <input type="number" class="form-control" id="remaining_amount" 
                                   step="0.01" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label for="payment_method" class="form-label">طريقة الدفع</label>
                            <select class="form-select" id="payment_method" name="payment_method">
                                <option value="cash">نقدي</option>
                                <option value="card">بطاقة</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" name="add_sale" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i>حفظ الفاتورة
                            </button>
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        // بيانات المنتجات
        const products = <?php echo json_encode($products); ?>;
        let productRowIndex = 0;
        
        // تهيئة Select2 للبحث المتطور
        $(document).ready(function() {
            // التحقق من وجود jQuery و Select2
            if (typeof $ === 'undefined') {
                console.error('jQuery غير محمل');
                return;
            }

            if (typeof $.fn.select2 === 'undefined') {
                console.error('Select2 غير محمل');
                // استخدام بحث عادي كبديل
                setupFallbackSearch();
                return;
            }

            try {
                $('#productSearchSelect').select2({
                    theme: 'bootstrap-5',
                    placeholder: 'ابحث عن منتج بالاسم، الباركود، أو الفئة...',
                    allowClear: true,
                    width: '100%',
                    language: {
                        noResults: function() {
                            return "لا توجد نتائج";
                        },
                        searching: function() {
                            return "جاري البحث...";
                        },
                        inputTooShort: function() {
                            return "اكتب حرفين على الأقل للبحث";
                        }
                    },
                    minimumInputLength: 1
                });

                console.log('تم تهيئة Select2 بنجاح');
            } catch (error) {
                console.error('خطأ في تهيئة Select2:', error);
                setupFallbackSearch();
            }

            // إنشاء أزرار الإضافة السريعة
            createQuickAddButtons();

            // تركيز على حقل الباركود
            $('#barcodeInput').focus();
        });
        
        // إعداد البحث البديل في حالة فشل Select2
        function setupFallbackSearch() {
            console.log('استخدام البحث البديل');
            const selectElement = document.getElementById('productSearchSelect');
            const container = selectElement.closest('.search-container');

            if (!container) {
                console.error('لم يتم العثور على حاوي البحث');
                return;
            }

            // تحويل Select إلى input للبحث
            const searchInput = document.createElement('input');
            searchInput.type = 'text';
            searchInput.className = 'form-control';
            searchInput.placeholder = 'ابحث عن منتج بالاسم أو الباركود...';
            searchInput.id = 'productSearchInput';

            // إنشاء قائمة النتائج
            const resultsDiv = document.createElement('div');
            resultsDiv.className = 'search-results';

            // استبدال Select بـ Input
            container.insertBefore(searchInput, selectElement);
            container.insertBefore(resultsDiv, selectElement.nextSibling);
            selectElement.style.display = 'none';

            // إضافة وظيفة البحث
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                resultsDiv.innerHTML = '';

                if (searchTerm.length < 2) {
                    resultsDiv.style.display = 'none';
                    return;
                }

                const filteredProducts = products.filter(product =>
                    product.name.toLowerCase().includes(searchTerm) ||
                    (product.barcode && product.barcode.toLowerCase().includes(searchTerm))
                );

                if (filteredProducts.length > 0) {
                    filteredProducts.slice(0, 8).forEach(product => {
                        const item = document.createElement('div');
                        item.className = 'search-result-item';
                        item.style.cssText = 'padding: 12px; cursor: pointer; border-bottom: 1px solid #eee; transition: all 0.2s ease;';

                        // تحديد لون المخزون
                        let stockColor = '#28a745'; // أخضر
                        let stockIcon = 'fas fa-check-circle';
                        if (product.stock_quantity <= 5) {
                            stockColor = '#ffc107'; // أصفر
                            stockIcon = 'fas fa-exclamation-triangle';
                        }
                        if (product.stock_quantity <= 0) {
                            stockColor = '#dc3545'; // أحمر
                            stockIcon = 'fas fa-times-circle';
                        }

                        item.innerHTML = `
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div style="font-weight: bold; color: #0d6efd; margin-bottom: 4px;">${product.name}</div>
                                    <div style="font-size: 0.85em; color: #6c757d; margin-bottom: 4px;">
                                        ${product.barcode ? `<i class="fas fa-barcode me-1"></i>باركود: ${product.barcode}` : '<i class="fas fa-info-circle me-1"></i>لا يوجد باركود'}
                                        ${product.category_name ? ` | <i class="fas fa-tag me-1"></i>${product.category_name}` : ''}
                                    </div>
                                    <div style="font-size: 0.85em;">
                                        <span style="background-color: #198754; color: white; padding: 2px 6px; border-radius: 3px; margin-left: 5px;">
                                            <i class="fas fa-money-bill-wave me-1"></i>${product.selling_price} ريال
                                        </span>
                                        <span style="background-color: ${stockColor}; color: white; padding: 2px 6px; border-radius: 3px;">
                                            <i class="${stockIcon} me-1"></i>${product.stock_quantity} ${product.unit || 'قطعة'}
                                        </span>
                                    </div>
                                </div>
                                <div style="text-align: center;">
                                    <button style="background: #0d6efd; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em;" type="button">
                                        <i class="fas fa-plus"></i> إضافة
                                    </button>
                                </div>
                            </div>
                        `;

                        item.addEventListener('click', function() {
                            addProductById(product.id);
                            searchInput.value = '';
                            resultsDiv.style.display = 'none';

                            // إظهار رسالة نجاح
                            showSuccessMessage(`تم إضافة ${product.name} للفاتورة`);
                        });

                        item.addEventListener('mouseenter', function() {
                            this.style.backgroundColor = '#f8f9fa';
                            this.style.transform = 'translateX(-2px)';
                            this.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                        });

                        item.addEventListener('mouseleave', function() {
                            this.style.backgroundColor = 'white';
                            this.style.transform = 'translateX(0)';
                            this.style.boxShadow = 'none';
                        });

                        resultsDiv.appendChild(item);
                    });
                    resultsDiv.style.display = 'block';
                } else {
                    resultsDiv.innerHTML = `
                        <div style="text-align: center; padding: 20px; color: #6c757d;">
                            <i class="fas fa-search" style="font-size: 2em; margin-bottom: 10px; display: block;"></i>
                            لا توجد نتائج للبحث عن "${searchTerm}"<br>
                            <small>جرب البحث بكلمات أخرى</small>
                        </div>
                    `;
                    resultsDiv.style.display = 'block';
                }
            });

            // إخفاء النتائج عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !resultsDiv.contains(e.target)) {
                    resultsDiv.style.display = 'none';
                }
            });
        }

        // إنشاء أزرار الإضافة السريعة للمنتجات الأكثر مبيعاً
        function createQuickAddButtons() {
            const quickAddContainer = document.getElementById('quickAddButtons');
            if (!quickAddContainer) {
                console.error('عنصر quickAddButtons غير موجود');
                return;
            }

            const topProducts = products.slice(0, 10); // أول 10 منتجات

            topProducts.forEach(product => {
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'quick-add-btn';
                button.textContent = product.name;
                button.onclick = () => addProductById(product.id);
                quickAddContainer.appendChild(button);
            });
        }
        
        // معالجة قارئ الباركود
        document.getElementById('barcodeInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const barcode = this.value.trim();
                if (barcode) {
                    addProductByBarcode(barcode);
                    this.value = '';
                }
            }
        });
        
        // إضافة منتج بالباركود
        function addProductByBarcode(barcode) {
            const product = products.find(p => p.code === barcode);
            if (product) {
                addProductToInvoice(product);
                showSuccessMessage(`تم إضافة ${product.name} للفاتورة`);
            } else {
                showErrorMessage(`لم يتم العثور على منتج بالكود: ${barcode}`);
            }
        }
        
        // إضافة المنتج المحدد من البحث المتطور
        function addSelectedProduct() {
            let selectedId = null;

            // التحقق من نوع البحث المستخدم
            if (typeof $ !== 'undefined' && $('#productSearchSelect').length && $('#productSearchSelect').is(':visible')) {
                // استخدام Select2
                selectedId = $('#productSearchSelect').val();
                if (selectedId) {
                    addProductById(selectedId);
                    $('#productSearchSelect').val(null).trigger('change');
                } else {
                    showErrorMessage('يرجى اختيار منتج من القائمة');
                }
            } else {
                // استخدام البحث البديل
                const searchInput = document.getElementById('productSearchInput');
                if (searchInput && searchInput.value.trim()) {
                    // البحث عن المنتج بالاسم أو الباركود
                    const searchTerm = searchInput.value.toLowerCase();
                    const foundProduct = products.find(product =>
                        product.name.toLowerCase().includes(searchTerm) ||
                        (product.barcode && product.barcode.toLowerCase().includes(searchTerm))
                    );

                    if (foundProduct) {
                        addProductById(foundProduct.id);
                        searchInput.value = '';
                        // إخفاء النتائج
                        const resultsDiv = searchInput.nextElementSibling;
                        if (resultsDiv) resultsDiv.style.display = 'none';
                    } else {
                        showErrorMessage('لم يتم العثور على المنتج');
                    }
                } else {
                    showErrorMessage('يرجى كتابة اسم المنتج أو اختياره من القائمة');
                }
            }
        }
        
        // إضافة منتج بالمعرف
        function addProductById(productId) {
            const product = products.find(p => p.id == productId);
            if (product) {
                addProductToInvoice(product);
            }
        }
        
        // إضافة منتج للفاتورة
        function addProductToInvoice(product) {
            // التحقق من وجود المنتج مسبقاً
            const existingRow = document.querySelector(`tr[data-product-id="${product.id}"]`);
            if (existingRow) {
                // زيادة الكمية
                const quantityInput = existingRow.querySelector('.quantity-input');
                quantityInput.value = parseFloat(quantityInput.value) + 1;
                calculateRowTotal(existingRow);
                showSuccessMessage(`تم زيادة كمية ${product.name}`);
                return;
            }
            
            // التحقق من المخزون
            if (product.stock_quantity <= 0) {
                showErrorMessage(`المنتج ${product.name} غير متوفر في المخزون`);
                return;
            }
            
            const tbody = document.getElementById('productsTableBody');
            const row = document.createElement('tr');
            row.setAttribute('data-product-id', product.id);
            
            const stockClass = product.stock_quantity <= 5 ? 'stock-warning' : 'stock-good';
            
            row.innerHTML = `
                <td>
                    <strong>${product.name}</strong>
                    ${product.code ? `<br><small class="text-muted">كود: ${product.code}</small>` : ''}
                    ${product.category_name ? `<br><small class="text-muted">فئة: ${product.category_name}</small>` : ''}
                    <input type="hidden" name="products[${productRowIndex}][product_id]" value="${product.id}">
                </td>
                <td>
                    <input type="number" class="form-control quantity-input" 
                           name="products[${productRowIndex}][quantity]" 
                           value="1" step="0.001" min="0.001" max="${product.stock_quantity}"
                           onchange="calculateRowTotal(this.closest('tr'))" required>
                </td>
                <td>
                    <input type="number" class="form-control price-input" 
                           name="products[${productRowIndex}][unit_price]" 
                           value="${product.selling_price}" step="0.01" min="0.01"
                           onchange="calculateRowTotal(this.closest('tr'))" required>
                </td>
                <td>
                    <input type="number" class="form-control total-input" 
                           value="${product.selling_price}" step="0.01" readonly>
                </td>
                <td>
                    <span class="badge bg-secondary">${product.unit || 'قطعة'}</span>
                </td>
                <td>
                    <span class="${stockClass}">${product.stock_quantity}</span>
                </td>
                <td>
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeProductRow(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            
            tbody.appendChild(row);
            productRowIndex++;
            
            // إخفاء رسالة الفراغ
            document.getElementById('emptyMessage').style.display = 'none';
            
            calculateTotal();
            updateItemsCount();
            showSuccessMessage(`تم إضافة ${product.name} للفاتورة`);
        }
        
        // حذف صف منتج
        function removeProductRow(button) {
            const row = button.closest('tr');
            const productName = row.querySelector('strong').textContent;
            row.remove();
            calculateTotal();
            updateItemsCount();
            
            // إظهار رسالة الفراغ إذا لم تعد هناك منتجات
            const tbody = document.getElementById('productsTableBody');
            if (tbody.children.length === 0) {
                document.getElementById('emptyMessage').style.display = '';
            }
            
            showSuccessMessage(`تم حذف ${productName} من الفاتورة`);
        }
        
        // حساب إجمالي الصف
        function calculateRowTotal(row) {
            const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
            const price = parseFloat(row.querySelector('.price-input').value) || 0;
            const total = quantity * price;
            
            row.querySelector('.total-input').value = total.toFixed(2);
            calculateTotal();
        }
        
        // حساب الإجمالي العام
        function calculateTotal() {
            const totalInputs = document.querySelectorAll('.total-input');
            let total = 0;
            
            totalInputs.forEach(input => {
                total += parseFloat(input.value) || 0;
            });
            
            document.getElementById('total_amount').value = total.toFixed(2);
            
            const discount = parseFloat(document.getElementById('discount_amount').value) || 0;
            const finalAmount = total - discount;
            
            document.getElementById('final_amount').value = finalAmount.toFixed(2);
            
            calculateRemaining();
        }
        
        // حساب المبلغ المتبقي
        function calculateRemaining() {
            const finalAmount = parseFloat(document.getElementById('final_amount').value) || 0;
            const paidAmount = parseFloat(document.getElementById('paid_amount').value) || 0;
            const remaining = finalAmount - paidAmount;
            
            document.getElementById('remaining_amount').value = remaining.toFixed(2);
        }
        
        // تحديث عدد الأصناف
        function updateItemsCount() {
            const count = document.querySelectorAll('#productsTableBody tr').length;
            document.getElementById('itemsCount').textContent = `${count} صنف`;
        }
        
        // عرض رسالة نجاح
        function showSuccessMessage(message) {
            console.log('Success:', message);

            // إنشاء toast notification
            const toast = document.createElement('div');
            toast.className = 'alert alert-success alert-dismissible fade show position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(toast);

            // إزالة الرسالة تلقائياً بعد 3 ثوان
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 3000);
        }

        // عرض رسالة خطأ
        function showErrorMessage(message) {
            console.log('Error:', message);

            // إنشاء toast notification للخطأ
            const toast = document.createElement('div');
            toast.className = 'alert alert-danger alert-dismissible fade show position-fixed';
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                <i class="fas fa-exclamation-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(toast);

            // إزالة الرسالة تلقائياً بعد 5 ثوان
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 5000);
        }
        
        // التحقق من صحة النموذج قبل الإرسال
        document.getElementById('saleForm').addEventListener('submit', function(e) {
            const products = document.querySelectorAll('#productsTableBody tr');
            
            if (products.length === 0) {
                e.preventDefault();
                alert('يجب إضافة منتج واحد على الأقل');
                return false;
            }
            
            const finalAmount = parseFloat(document.getElementById('final_amount').value) || 0;
            if (finalAmount <= 0) {
                e.preventDefault();
                alert('المبلغ النهائي يجب أن يكون أكبر من صفر');
                return false;
            }
        });
        
        // تركيز تلقائي على حقل الباركود عند الضغط على F2
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F2') {
                e.preventDefault();
                document.getElementById('barcodeInput').focus();
            }
        });
    </script>

</body>
</html>
