<?php
/**
 * صفحة إضافة مبيعة جديدة مع نظام الرصيد المتقدم
 * Add New Sale Page with Advanced Balance System
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";
require_once "../../includes/advanced-balance-functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "فاتورة مبيعة جديدة - نظام متقدم";
$page_icon = "fas fa-cash-register";

// معالجة إضافة المبيعة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_sale'])) {
    try {
        $conn->begin_transaction();
        
        // بيانات المبيعة الأساسية
        $customer_id = intval($_POST['customer_id']);
        $sale_date = clean($conn, $_POST['sale_date']);
        $total_amount = floatval($_POST['total_amount']);
        $discount_amount = floatval($_POST['discount_amount']);
        $final_amount = $total_amount - $discount_amount;
        $notes = clean($conn, $_POST['notes']);
        
        // بيانات الدفع المتقدمة
        $cash_paid = floatval($_POST['cash_paid']);
        $use_wallet = isset($_POST['use_wallet']) ? true : false;
        $payment_method = clean($conn, $_POST['payment_method']);
        
        // إنشاء رقم المبيعة
        $sale_number = generateSaleNumber($conn);
        
        // إدراج المبيعة الأساسية
        $insert_sale = "INSERT INTO sales (sale_number, customer_id, sale_date, total_amount, discount_amount, 
                                          final_amount, paid_amount, payment_method, notes, user_id) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($insert_sale);
        $stmt->bind_param("sisddddss", $sale_number, $customer_id, $sale_date, $total_amount, 
                         $discount_amount, $final_amount, $cash_paid, $payment_method, $notes, $_SESSION['user_id']);
        $stmt->execute();
        $sale_id = $conn->insert_id;
        
        // إدراج تفاصيل المنتجات
        if (isset($_POST['products']) && is_array($_POST['products'])) {
            foreach ($_POST['products'] as $product) {
                $product_id = intval($product['id']);
                $quantity = floatval($product['quantity']);
                $unit_price = floatval($product['price']);
                $total_price = $quantity * $unit_price;
                
                if ($quantity > 0) {
                    // إدراج تفاصيل المبيعة
                    $insert_detail = "INSERT INTO sale_details (sale_id, product_id, quantity, unit_price, total_price) 
                                     VALUES (?, ?, ?, ?, ?)";
                    $detail_stmt = $conn->prepare($insert_detail);
                    $detail_stmt->bind_param("iiddd", $sale_id, $product_id, $quantity, $unit_price, $total_price);
                    $detail_stmt->execute();
                    
                    // تحديث المخزون
                    $update_stock = "UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?";
                    $stock_stmt = $conn->prepare($update_stock);
                    $stock_stmt->bind_param("di", $quantity, $product_id);
                    $stock_stmt->execute();
                }
            }
        }
        
        // معالجة الدفع المتقدم
        $paymentResult = ['success' => true, 'cash_paid' => $cash_paid, 'wallet_used' => 0, 'change_amount' => 0, 'wallet_added' => 0, 'debt_amount' => 0, 'message' => 'تم الدفع بالكامل'];

        if ($customer_id > 1) { // ليس عميل نقدي
            $paymentResult = processAdvancedPayment(
                'customer',
                $customer_id,
                $final_amount,
                $cash_paid,
                $use_wallet,
                'sale',
                $sale_id,
                $_SESSION['user_id']
            );

            if ($paymentResult['success']) {
                // تحديث تفاصيل الدفع في جدول المبيعات
                $update_payment = "UPDATE sales SET
                                  cash_paid = ?,
                                  wallet_used = ?,
                                  change_amount = ?,
                                  wallet_added = ?,
                                  debt_amount = ?,
                                  paid_amount = ?
                                  WHERE id = ?";

                $total_paid = $paymentResult['cash_paid'] + $paymentResult['wallet_used'];
                $payment_stmt = $conn->prepare($update_payment);
                $payment_stmt->bind_param("ddddddi",
                    $paymentResult['cash_paid'],
                    $paymentResult['wallet_used'],
                    $paymentResult['change_amount'],
                    $paymentResult['wallet_added'],
                    $paymentResult['debt_amount'],
                    $total_paid,
                    $sale_id
                );
                $payment_stmt->execute();

                $_SESSION['payment_message'] = $paymentResult['message'];
            }
        }

        // تحديث الخزينة (فقط المبلغ النقدي المدفوع فعلياً)
        $actual_cash = $cash_paid - ($paymentResult['wallet_added'] ?? 0);
        if ($actual_cash > 0) {
            $description = "مبيعة رقم: $sale_number";
            addTreasuryTransaction('sales', $sale_id, $actual_cash, $description, $_SESSION['user_id'], $sale_date);
        }
        
        $conn->commit();
        $_SESSION['success_message'] = "تم إضافة المبيعة بنجاح برقم: $sale_number";
        header("Location: view.php?id=$sale_id");
        exit();
        
    } catch (Exception $e) {
        $conn->rollback();
        $_SESSION['error_message'] = "خطأ في إضافة المبيعة: " . $e->getMessage();
    }
}

// الحصول على العملاء
$customers_query = "SELECT id, name, phone, wallet_balance, debt_balance FROM customers ORDER BY name";
$customers_result = $conn->query($customers_query);

// الحصول على المنتجات
$products_query = "SELECT p.*, c.name as category_name 
                  FROM products p 
                  LEFT JOIN categories c ON p.category_id = c.id 
                  WHERE p.stock_quantity > 0 
                  ORDER BY p.name";
$products_result = $conn->query($products_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .balance-info {
            background: #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .wallet-option {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .payment-summary {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .product-row {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">نظام دفع متقدم مع إدارة الرصيد والديون</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="index.php" class="btn btn-light btn-lg me-2">
                        <i class="fas fa-list me-2"></i>قائمة المبيعات
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-home me-2"></i>الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['payment_message'])): ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <?php echo $_SESSION['payment_message']; unset($_SESSION['payment_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <form method="POST" id="saleForm">
            <div class="row">
                
                <!-- معلومات المبيعة -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-shopping-cart me-2"></i>
                                تفاصيل المبيعة
                            </h5>
                        </div>
                        <div class="card-body">
                            
                            <!-- معلومات أساسية -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="customer_id" class="form-label">العميل <span class="text-danger">*</span></label>
                                    <select class="form-select" id="customer_id" name="customer_id" required onchange="updateCustomerBalance()">
                                        <option value="">اختر العميل</option>
                                        <?php while ($customer = $customers_result->fetch_assoc()): ?>
                                            <option value="<?php echo $customer['id']; ?>" 
                                                    data-wallet="<?php echo $customer['wallet_balance']; ?>"
                                                    data-debt="<?php echo $customer['debt_balance']; ?>">
                                                <?php echo htmlspecialchars($customer['name']); ?>
                                                <?php if ($customer['phone']): ?>
                                                    - <?php echo htmlspecialchars($customer['phone']); ?>
                                                <?php endif; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="sale_date" class="form-label">تاريخ المبيعة</label>
                                    <input type="date" class="form-control" id="sale_date" name="sale_date" 
                                           value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>

                            <!-- معلومات رصيد العميل -->
                            <div id="customer-balance-info" class="balance-info" style="display: none;">
                                <h6><i class="fas fa-wallet me-2"></i>معلومات رصيد العميل</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <small class="text-muted">الرصيد المحفوظ:</small>
                                        <div class="fw-bold text-success" id="wallet-balance">0.00 ريال</div>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">الديون المستحقة:</small>
                                        <div class="fw-bold text-danger" id="debt-balance">0.00 ريال</div>
                                    </div>
                                    <div class="col-md-4">
                                        <small class="text-muted">الرصيد الصافي:</small>
                                        <div class="fw-bold" id="net-balance">0.00 ريال</div>
                                    </div>
                                </div>
                            </div>

                            <!-- المنتجات -->
                            <div class="mb-4">
                                <h6><i class="fas fa-boxes me-2"></i>المنتجات</h6>
                                <div id="products-container">
                                    <!-- سيتم إضافة المنتجات هنا بواسطة JavaScript -->
                                </div>
                                <button type="button" class="btn btn-outline-primary" onclick="addProductRow()">
                                    <i class="fas fa-plus me-2"></i>إضافة منتج
                                </button>
                            </div>

                            <!-- الملاحظات -->
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص المبيعة والدفع -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-calculator me-2"></i>
                                ملخص المبيعة
                            </h5>
                        </div>
                        <div class="card-body">
                            
                            <!-- ملخص المبالغ -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الإجمالي:</span>
                                    <span class="fw-bold" id="total-amount">0.00 ريال</span>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="discount_amount" class="form-label">الخصم</label>
                                    <input type="number" class="form-control" id="discount_amount" name="discount_amount" 
                                           value="0" min="0" step="0.01" onchange="calculateTotals()">
                                </div>
                                
                                <div class="d-flex justify-content-between mb-3 border-top pt-2">
                                    <span class="fw-bold">المبلغ النهائي:</span>
                                    <span class="fw-bold text-primary" id="final-amount">0.00 ريال</span>
                                </div>
                            </div>

                            <!-- خيارات الدفع -->
                            <div class="mb-3">
                                <h6><i class="fas fa-credit-card me-2"></i>طريقة الدفع</h6>
                                <select class="form-select mb-3" id="payment_method" name="payment_method" required>
                                    <option value="نقدي">نقدي</option>
                                    <option value="آجل">آجل</option>
                                    <option value="مختلط">مختلط</option>
                                </select>
                            </div>

                            <!-- خيار استخدام الرصيد المحفوظ -->
                            <div id="wallet-option" class="wallet-option" style="display: none;">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="use_wallet" name="use_wallet" onchange="calculatePayment()">
                                    <label class="form-check-label" for="use_wallet">
                                        <strong>استخدام الرصيد المحفوظ</strong>
                                        <br><small class="text-muted">المتاح: <span id="available-wallet">0.00</span> ريال</small>
                                    </label>
                                </div>
                            </div>

                            <!-- المبلغ المدفوع نقداً -->
                            <div class="mb-3">
                                <label for="cash_paid" class="form-label">المبلغ المدفوع نقداً</label>
                                <input type="number" class="form-control" id="cash_paid" name="cash_paid" 
                                       value="0" min="0" step="0.01" onchange="calculatePayment()">
                            </div>

                            <!-- ملخص الدفع -->
                            <div class="payment-summary">
                                <h6>ملخص الدفع:</h6>
                                <div class="d-flex justify-content-between">
                                    <small>نقدي:</small>
                                    <small id="summary-cash">0.00 ريال</small>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <small>من الرصيد:</small>
                                    <small id="summary-wallet">0.00 ريال</small>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <small>إجمالي المدفوع:</small>
                                    <small class="fw-bold" id="summary-total-paid">0.00 ريال</small>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <small>المتبقي:</small>
                                    <small class="fw-bold text-danger" id="summary-remaining">0.00 ريال</small>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <small>الفكة:</small>
                                    <small class="fw-bold text-success" id="summary-change">0.00 ريال</small>
                                </div>
                            </div>

                            <!-- أزرار الحفظ -->
                            <div class="d-grid gap-2 mt-4">
                                <button type="submit" name="add_sale" class="btn btn-success btn-lg">
                                    <i class="fas fa-save me-2"></i>حفظ المبيعة
                                </button>
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- حقول مخفية -->
            <input type="hidden" id="total_amount" name="total_amount" value="0">
        </form>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // بيانات المنتجات
        const products = <?php 
            $products_result->data_seek(0);
            $products_array = [];
            while ($product = $products_result->fetch_assoc()) {
                $products_array[] = $product;
            }
            echo json_encode($products_array);
        ?>;

        let productRowCounter = 0;

        // إضافة صف منتج جديد
        function addProductRow() {
            productRowCounter++;
            const container = document.getElementById('products-container');
            
            const productRow = document.createElement('div');
            productRow.className = 'product-row';
            productRow.id = `product-row-${productRowCounter}`;
            
            productRow.innerHTML = `
                <div class="row align-items-center">
                    <div class="col-md-5">
                        <select class="form-select" name="products[${productRowCounter}][id]" onchange="updateProductPrice(${productRowCounter})" required>
                            <option value="">اختر المنتج</option>
                            ${products.map(product => 
                                `<option value="${product.id}" data-price="${product.selling_price}" data-stock="${product.stock_quantity}">
                                    ${product.name} - ${product.stock_quantity} متاح
                                </option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="number" class="form-control" name="products[${productRowCounter}][quantity]" 
                               placeholder="الكمية" min="1" step="1" onchange="calculateRowTotal(${productRowCounter})" required>
                    </div>
                    <div class="col-md-2">
                        <input type="number" class="form-control" name="products[${productRowCounter}][price]" 
                               placeholder="السعر" min="0" step="0.01" onchange="calculateRowTotal(${productRowCounter})" required>
                    </div>
                    <div class="col-md-2">
                        <span class="fw-bold" id="row-total-${productRowCounter}">0.00</span>
                    </div>
                    <div class="col-md-1">
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeProductRow(${productRowCounter})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            
            container.appendChild(productRow);
        }

        // تحديث سعر المنتج عند الاختيار
        function updateProductPrice(rowId) {
            const select = document.querySelector(`#product-row-${rowId} select`);
            const priceInput = document.querySelector(`#product-row-${rowId} input[name*="[price]"]`);
            
            if (select.value) {
                const selectedOption = select.options[select.selectedIndex];
                const price = selectedOption.getAttribute('data-price');
                priceInput.value = price;
                calculateRowTotal(rowId);
            }
        }

        // حساب إجمالي الصف
        function calculateRowTotal(rowId) {
            const quantityInput = document.querySelector(`#product-row-${rowId} input[name*="[quantity]"]`);
            const priceInput = document.querySelector(`#product-row-${rowId} input[name*="[price]"]`);
            const totalSpan = document.getElementById(`row-total-${rowId}`);
            
            const quantity = parseFloat(quantityInput.value) || 0;
            const price = parseFloat(priceInput.value) || 0;
            const total = quantity * price;
            
            totalSpan.textContent = total.toFixed(2);
            calculateTotals();
        }

        // حساب الإجماليات
        function calculateTotals() {
            let total = 0;
            
            // جمع إجماليات جميع الصفوف
            document.querySelectorAll('[id^="row-total-"]').forEach(span => {
                total += parseFloat(span.textContent) || 0;
            });
            
            const discount = parseFloat(document.getElementById('discount_amount').value) || 0;
            const finalAmount = total - discount;
            
            document.getElementById('total-amount').textContent = total.toFixed(2) + ' ريال';
            document.getElementById('final-amount').textContent = finalAmount.toFixed(2) + ' ريال';
            document.getElementById('total_amount').value = total;
            
            calculatePayment();
        }

        // حساب تفاصيل الدفع
        function calculatePayment() {
            const finalAmount = parseFloat(document.getElementById('total_amount').value) - 
                               (parseFloat(document.getElementById('discount_amount').value) || 0);
            const cashPaid = parseFloat(document.getElementById('cash_paid').value) || 0;
            const useWallet = document.getElementById('use_wallet').checked;
            const availableWallet = parseFloat(document.getElementById('available-wallet').textContent) || 0;
            
            let walletUsed = 0;
            if (useWallet && availableWallet > 0) {
                walletUsed = Math.min(finalAmount, availableWallet);
            }
            
            const totalPaid = cashPaid + walletUsed;
            const remaining = Math.max(0, finalAmount - totalPaid);
            const change = Math.max(0, totalPaid - finalAmount);
            
            // تحديث ملخص الدفع
            document.getElementById('summary-cash').textContent = cashPaid.toFixed(2) + ' ريال';
            document.getElementById('summary-wallet').textContent = walletUsed.toFixed(2) + ' ريال';
            document.getElementById('summary-total-paid').textContent = totalPaid.toFixed(2) + ' ريال';
            document.getElementById('summary-remaining').textContent = remaining.toFixed(2) + ' ريال';
            document.getElementById('summary-change').textContent = change.toFixed(2) + ' ريال';
        }

        // تحديث معلومات رصيد العميل
        function updateCustomerBalance() {
            const select = document.getElementById('customer_id');
            const balanceInfo = document.getElementById('customer-balance-info');
            const walletOption = document.getElementById('wallet-option');
            
            if (select.value && select.value != '1') { // ليس عميل نقدي
                const selectedOption = select.options[select.selectedIndex];
                const walletBalance = parseFloat(selectedOption.getAttribute('data-wallet')) || 0;
                const debtBalance = parseFloat(selectedOption.getAttribute('data-debt')) || 0;
                const netBalance = walletBalance - debtBalance;
                
                // تحديث عرض الأرصدة
                document.getElementById('wallet-balance').textContent = walletBalance.toFixed(2) + ' ريال';
                document.getElementById('debt-balance').textContent = debtBalance.toFixed(2) + ' ريال';
                document.getElementById('net-balance').textContent = netBalance.toFixed(2) + ' ريال';
                document.getElementById('net-balance').className = netBalance >= 0 ? 'fw-bold text-success' : 'fw-bold text-danger';
                
                // إظهار معلومات الرصيد
                balanceInfo.style.display = 'block';
                
                // إظهار خيار استخدام الرصيد إذا كان متاحاً
                if (walletBalance >= 10) { // الحد الأدنى
                    document.getElementById('available-wallet').textContent = walletBalance.toFixed(2);
                    walletOption.style.display = 'block';
                } else {
                    walletOption.style.display = 'none';
                    document.getElementById('use_wallet').checked = false;
                }
            } else {
                balanceInfo.style.display = 'none';
                walletOption.style.display = 'none';
                document.getElementById('use_wallet').checked = false;
            }
            
            calculatePayment();
        }

        // حذف صف منتج
        function removeProductRow(rowId) {
            const row = document.getElementById(`product-row-${rowId}`);
            if (row) {
                row.remove();
                calculateTotals();
            }
        }

        // إضافة صف منتج افتراضي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addProductRow();
        });
    </script>

</body>
</html>
