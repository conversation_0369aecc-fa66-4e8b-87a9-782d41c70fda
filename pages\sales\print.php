<?php
/**
 * صفحة طباعة فاتورة المبيعة
 * Print Sale Invoice Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

// التحقق من وجود معرف المبيعة
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo "معرف المبيعة غير صحيح";
    exit();
}

$sale_id = intval($_GET['id']);

// الحصول على بيانات المبيعة
$sale_query = "SELECT s.*, c.name as customer_name, c.phone as customer_phone, c.address as customer_address, 
               u.name as user_name 
               FROM sales s 
               LEFT JOIN customers c ON s.customer_id = c.id 
               LEFT JOIN users u ON s.user_id = u.id 
               WHERE s.id = ?";

$stmt = $conn->prepare($sale_query);
$stmt->bind_param("i", $sale_id);
$stmt->execute();
$sale_result = $stmt->get_result();

if ($sale_result->num_rows === 0) {
    echo "المبيعة غير موجودة";
    exit();
}

$sale = $sale_result->fetch_assoc();

// الحصول على أصناف المبيعة
$items_query = "SELECT si.*, p.name as product_name, p.unit 
                FROM sale_items si 
                LEFT JOIN products p ON si.product_id = p.id 
                WHERE si.sale_id = ? 
                ORDER BY si.id";

$stmt = $conn->prepare($items_query);
$stmt->bind_param("i", $sale_id);
$stmt->execute();
$items_result = $stmt->get_result();

// الحصول على إعدادات المتجر
$store_name = getSetting('store_name', 'متجر Zero');
$store_address = getSetting('store_address', '');
$store_phone = getSetting('store_phone', '');
$store_currency = getSetting('store_currency', 'ريال');
$receipt_footer = getSetting('receipt_footer', 'شكراً لزيارتكم');
$store_logo = getSetting('store_logo', '');

// حساب المبلغ المتبقي
$remaining_amount = $sale['final_amount'] - $sale['paid_amount'];

// الحصول على رصيد العميل الحالي إذا كان موجوداً
$customer_balance = 0;
if (!empty($sale['customer_id'])) {
    $balance_query = "SELECT balance FROM customers WHERE id = ?";
    $balance_stmt = $conn->prepare($balance_query);
    $balance_stmt->bind_param("i", $sale['customer_id']);
    $balance_stmt->execute();
    $balance_result = $balance_stmt->get_result();
    if ($balance_result->num_rows > 0) {
        $customer_balance = $balance_result->fetch_assoc()['balance'];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة مبيعة رقم: <?php echo htmlspecialchars($sale['sale_number']); ?></title>
    
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
            color: #333;
            font-size: 14px;
            line-height: 1.4;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
        }

        .invoice-header {
            padding: 30px;
            border-bottom: 3px solid #667eea;
            background: #f8f9fa;
        }

        .header-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .store-info {
            flex: 1;
            text-align: right;
            padding-right: 20px;
        }

        .store-name {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .store-details {
            font-size: 12px;
            color: #666;
            line-height: 1.6;
        }

        .logo-section {
            flex: 1;
            text-align: center;
            padding: 0 20px;
        }

        .store-logo {
            max-width: 120px;
            max-height: 80px;
            border-radius: 5px;
        }

        .logo-placeholder {
            width: 120px;
            height: 80px;
            border: 2px dashed #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 12px;
            margin: 0 auto;
        }

        .invoice-info {
            flex: 1;
            text-align: left;
            padding-left: 20px;
        }

        .invoice-title {
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .invoice-meta {
            font-size: 12px;
            color: #666;
            line-height: 1.6;
        }

        .invoice-details {
            padding: 30px;
        }
        
        .customer-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
        }

        .customer-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }

        .customer-details {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .customer-basic {
            flex: 1;
        }

        .customer-balance {
            flex: 1;
            text-align: left;
            padding-left: 20px;
        }

        .balance-info {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        .balance-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .balance-amount {
            font-size: 18px;
            font-weight: bold;
        }

        .balance-credit {
            color: #28a745;
        }

        .balance-debit {
            color: #dc3545;
        }

        .balance-zero {
            color: #6c757d;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }

        .items-table th {
            background: #667eea;
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
        }
        
        .items-table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .summary-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        
        .summary-row.total {
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
            border-top: 2px solid #667eea;
            padding-top: 15px;
            margin-top: 15px;
        }
        
        .payment-status {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .status-paid {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-partial {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-unpaid {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .invoice-footer {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            color: #666;
        }
        
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .signature-box {
            text-align: center;
            width: 200px;
        }
        
        .signature-line {
            border-bottom: 1px solid #333;
            margin-bottom: 10px;
            height: 40px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .invoice-container {
                border: none;
                border-radius: 0;
                box-shadow: none;
            }
            
            .no-print {
                display: none !important;
            }
        }
        
        @media (max-width: 768px) {
            .invoice-meta {
                flex-direction: column;
                gap: 20px;
            }
            
            .items-table {
                font-size: 12px;
            }
            
            .items-table th,
            .items-table td {
                padding: 8px 5px;
            }
        }
    </style>
</head>
<body>
    
    <div class="invoice-container">
        
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="header-row">
                <!-- بيانات المحل -->
                <div class="store-info">
                    <div class="store-name"><?php echo htmlspecialchars($store_name); ?></div>
                    <div class="store-details">
                        <?php if (!empty($store_address)): ?>
                            <div><?php echo htmlspecialchars($store_address); ?></div>
                        <?php endif; ?>
                        <?php if (!empty($store_phone)): ?>
                            <div>هاتف: <?php echo htmlspecialchars($store_phone); ?></div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- الشعار -->
                <div class="logo-section">
                    <?php if (!empty($store_logo) && file_exists("../../" . $store_logo)): ?>
                        <img src="../../<?php echo $store_logo; ?>" alt="شعار المحل" class="store-logo">
                    <?php else: ?>
                        <div class="logo-placeholder">
                            شعار المحل
                        </div>
                    <?php endif; ?>
                </div>

                <!-- بيانات الفاتورة -->
                <div class="invoice-info">
                    <div class="invoice-title">فاتورة مبيعة</div>
                    <div class="invoice-meta">
                        <div><strong>رقم الفاتورة:</strong> <?php echo $sale['id']; ?></div>
                        <div><strong>التاريخ:</strong> <?php echo date('Y-m-d', strtotime($sale['sale_date'])); ?></div>
                        <div><strong>الوقت:</strong> <?php echo date('H:i', strtotime($sale['created_at'])); ?></div>
                        <div><strong>الكاشير:</strong> <?php echo htmlspecialchars($sale['user_name']); ?></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- تفاصيل الفاتورة -->
        <div class="invoice-details">
            
            <!-- جدول الأصناف -->
            <table class="items-table">
                <thead>
                    <tr>
                        <th>م</th>
                        <th>المنتج</th>
                        <th>الكمية</th>
                        <th>الوحدة</th>
                        <th>سعر الوحدة</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($items_result->num_rows > 0): ?>
                        <?php $counter = 1; ?>
                        <?php while ($item = $items_result->fetch_assoc()): ?>
                            <tr>
                                <td><?php echo $counter++; ?></td>
                                <td style="text-align: right;"><?php echo htmlspecialchars($item['product_name']); ?></td>
                                <td><?php echo number_format($item['quantity'], 3); ?></td>
                                <td><?php echo htmlspecialchars($item['unit'] ?? 'قطعة'); ?></td>
                                <td><?php echo number_format($item['unit_price'], 2); ?> <?php echo $store_currency; ?></td>
                                <td><?php echo number_format($item['total_price'], 2); ?> <?php echo $store_currency; ?></td>
                            </tr>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" style="text-align: center; color: #666;">لا توجد أصناف</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
            
            <!-- ملخص المبالغ -->
            <div class="summary-section">
                <div class="summary-row">
                    <span>المبلغ الإجمالي:</span>
                    <span><?php echo number_format($sale['total_amount'], 2); ?> <?php echo $store_currency; ?></span>
                </div>
                
                <?php if ($sale['discount_amount'] > 0): ?>
                    <div class="summary-row">
                        <span>مبلغ الخصم:</span>
                        <span style="color: #dc3545;">- <?php echo number_format($sale['discount_amount'], 2); ?> <?php echo $store_currency; ?></span>
                    </div>
                <?php endif; ?>
                
                <div class="summary-row total">
                    <span>المبلغ النهائي:</span>
                    <span><?php echo number_format($sale['final_amount'], 2); ?> <?php echo $store_currency; ?></span>
                </div>
                
                <div class="summary-row">
                    <span>المبلغ المدفوع:</span>
                    <span style="color: #28a745;"><?php echo number_format($sale['paid_amount'], 2); ?> <?php echo $store_currency; ?></span>
                </div>
                
                <?php if ($remaining_amount > 0): ?>
                    <div class="summary-row">
                        <span>المبلغ المتبقي:</span>
                        <span style="color: #ffc107;"><?php echo number_format($remaining_amount, 2); ?> <?php echo $store_currency; ?></span>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- حالة الدفع -->
            <div class="payment-status <?php 
                if ($remaining_amount <= 0) echo 'status-paid';
                elseif ($sale['paid_amount'] > 0) echo 'status-partial';
                else echo 'status-unpaid';
            ?>">
                <?php if ($remaining_amount <= 0): ?>
                    ✅ تم الدفع بالكامل
                <?php elseif ($sale['paid_amount'] > 0): ?>
                    ⚠️ دفع جزئي - المتبقي: <?php echo number_format($remaining_amount, 2); ?> <?php echo $store_currency; ?>
                <?php else: ?>
                    ❌ لم يتم الدفع
                <?php endif; ?>
            </div>
            
            <!-- طريقة الدفع -->
            <div style="text-align: center; margin-bottom: 20px;">
                <strong>طريقة الدفع:</strong>
                <?php
                $payment_methods = [
                    'cash' => 'نقدي',
                    'card' => 'بطاقة',
                    'bank_transfer' => 'تحويل بنكي'
                ];
                echo $payment_methods[$sale['payment_method']] ?? $sale['payment_method'];
                ?>
            </div>
            
            <!-- ملاحظات -->
            <?php if (!empty($sale['notes'])): ?>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <strong>ملاحظات:</strong><br>
                    <?php echo nl2br(htmlspecialchars($sale['notes'])); ?>
                </div>
            <?php endif; ?>
            
            <!-- معلومات العميل وكشف الحساب -->
            <?php if (!empty($sale['customer_id'])): ?>
                <div class="customer-info">
                    <div class="customer-title">معلومات العميل وكشف الحساب</div>
                    <div class="customer-details">
                        <div class="customer-basic">
                            <div><strong>اسم العميل:</strong> <?php echo htmlspecialchars($sale['customer_name']); ?></div>
                            <?php if (!empty($sale['customer_phone'])): ?>
                                <div><strong>رقم الهاتف:</strong> <?php echo htmlspecialchars($sale['customer_phone']); ?></div>
                            <?php endif; ?>
                            <?php if (!empty($sale['customer_address'])): ?>
                                <div><strong>العنوان:</strong> <?php echo htmlspecialchars($sale['customer_address']); ?></div>
                            <?php endif; ?>
                        </div>
                        <div class="customer-balance">
                            <div class="balance-info">
                                <div class="balance-title">كشف حساب العميل</div>
                                <div class="balance-amount <?php
                                    if ($customer_balance > 0) echo 'balance-credit';
                                    elseif ($customer_balance < 0) echo 'balance-debit';
                                    else echo 'balance-zero';
                                ?>">
                                    <?php
                                    if ($customer_balance > 0) {
                                        echo "دائن: " . number_format($customer_balance, 2) . " " . $store_currency;
                                    } elseif ($customer_balance < 0) {
                                        echo "مدين: " . number_format(abs($customer_balance), 2) . " " . $store_currency;
                                    } else {
                                        echo "متعادل: 0.00 " . $store_currency;
                                    }
                                    ?>
                                </div>
                                <div style="font-size: 12px; color: #666; margin-top: 5px;">
                                    <?php
                                    if ($customer_balance > 0) {
                                        echo "العميل له رصيد لدى المحل";
                                    } elseif ($customer_balance < 0) {
                                        echo "العميل عليه مبلغ للمحل";
                                    } else {
                                        echo "لا يوجد رصيد مستحق";
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- التوقيعات -->
            <div class="signature-section">
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div>توقيع العميل</div>
                </div>
                <div class="signature-box">
                    <div class="signature-line"></div>
                    <div>توقيع الكاشير</div>
                </div>
            </div>
            
        </div>
        
        <!-- تذييل الفاتورة -->
        <div class="invoice-footer">
            <div><?php echo htmlspecialchars($receipt_footer); ?></div>
            <div style="margin-top: 10px; font-size: 12px; color: #999;">
                تم الطباعة في: <?php echo date('Y-m-d H:i:s'); ?>
            </div>
        </div>
        
    </div>
    
    <!-- أزرار التحكم -->
    <div class="no-print" style="text-align: center; margin: 20px 0;">
        <button onclick="window.print()" style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 0 10px; cursor: pointer;">
            🖨️ طباعة
        </button>
        <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin: 0 10px; cursor: pointer;">
            ❌ إغلاق
        </button>
        <a href="view.php?id=<?php echo $sale['id']; ?>" style="background: #28a745; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; margin: 0 10px; display: inline-block;">
            👁️ عرض التفاصيل
        </a>
    </div>
    
    <script>
        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        // window.onload = function() { window.print(); }
    </script>

</body>
</html>
