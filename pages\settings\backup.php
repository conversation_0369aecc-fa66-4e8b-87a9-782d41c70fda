<?php
/**
 * نظام النسخ الاحتياطي المتطور
 * Advanced Backup System
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";
require_once "../../includes/session-helper.php";

// التحقق من تسجيل الدخول وصلاحية المدير
requireAdmin();

$page_title = "النسخ الاحتياطي";
$page_icon = "fas fa-download";

// التأكد من توفر اسم قاعدة البيانات
if (!isset($db_name)) {
    $db_name = 'zero'; // القيمة الافتراضية
}

// إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
$backup_dir = "../../backups/";
if (!is_dir($backup_dir)) {
    mkdir($backup_dir, 0755, true);
}

$message = '';
$message_type = '';

// معالجة إنشاء نسخة احتياطية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_backup'])) {
    try {
        $backup_type = $_POST['backup_type'] ?? 'full';
        $include_files = isset($_POST['include_files']);
        
        // إنشاء اسم الملف
        $timestamp = date('Y-m-d_H-i-s');
        $backup_filename = "zero_backup_{$backup_type}_{$timestamp}.sql";
        $backup_path = $backup_dir . $backup_filename;
        
        // إنشاء النسخة الاحتياطية
        $backup_content = createDatabaseBackup($conn, $backup_type);
        
        if (file_put_contents($backup_path, $backup_content)) {
            // إنشاء ملف معلومات النسخة
            $info_content = [
                'filename' => $backup_filename,
                'type' => $backup_type,
                'size' => filesize($backup_path),
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => $_SESSION['user_name'],
                'user_id' => $_SESSION['user_id'],
                'include_files' => $include_files,
                'database_name' => $db_name,
                'version' => '1.0'
            ];
            
            file_put_contents($backup_dir . "info_{$timestamp}.json", json_encode($info_content, JSON_PRETTY_PRINT));
            
            // نسخ الملفات إذا كان مطلوباً
            if ($include_files) {
                $files_backup = createFilesBackup($timestamp);
                $info_content['files_backup'] = $files_backup;
                file_put_contents($backup_dir . "info_{$timestamp}.json", json_encode($info_content, JSON_PRETTY_PRINT));
            }
            
            $message = "تم إنشاء النسخة الاحتياطية بنجاح: " . $backup_filename;
            $message_type = 'success';
        } else {
            throw new Exception("فشل في حفظ النسخة الاحتياطية");
        }
        
    } catch (Exception $e) {
        $message = "خطأ في إنشاء النسخة الاحتياطية: " . $e->getMessage();
        $message_type = 'danger';
    }
}

// معالجة استعادة النسخة الاحتياطية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['restore_backup'])) {
    try {
        $backup_file = $_POST['backup_file'];
        $backup_path = $backup_dir . $backup_file;
        
        if (!file_exists($backup_path)) {
            throw new Exception("ملف النسخة الاحتياطية غير موجود");
        }
        
        // قراءة محتوى النسخة الاحتياطية
        $backup_content = file_get_contents($backup_path);
        
        // تنفيذ استعادة البيانات
        $conn->begin_transaction();
        
        // تقسيم الاستعلامات وتنفيذها
        $queries = explode(';', $backup_content);
        foreach ($queries as $query) {
            $query = trim($query);
            if (!empty($query)) {
                $conn->query($query);
            }
        }
        
        $conn->commit();
        
        $message = "تم استعادة النسخة الاحتياطية بنجاح";
        $message_type = 'success';
        
    } catch (Exception $e) {
        $conn->rollback();
        $message = "خطأ في استعادة النسخة الاحتياطية: " . $e->getMessage();
        $message_type = 'danger';
    }
}

// معالجة حذف النسخة الاحتياطية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_backup'])) {
    try {
        $backup_file = $_POST['backup_file'];
        $backup_path = $backup_dir . $backup_file;
        
        if (file_exists($backup_path)) {
            unlink($backup_path);
            
            // حذف ملف المعلومات المرتبط
            $info_file = str_replace('.sql', '.json', $backup_file);
            $info_file = str_replace('zero_backup_', 'info_', $info_file);
            $info_path = $backup_dir . $info_file;
            if (file_exists($info_path)) {
                unlink($info_path);
            }
            
            $message = "تم حذف النسخة الاحتياطية بنجاح";
            $message_type = 'success';
        } else {
            throw new Exception("ملف النسخة الاحتياطية غير موجود");
        }
        
    } catch (Exception $e) {
        $message = "خطأ في حذف النسخة الاحتياطية: " . $e->getMessage();
        $message_type = 'danger';
    }
}

// الحصول على قائمة النسخ الاحتياطية
function getBackupsList($backup_dir) {
    $backups = [];
    $files = glob($backup_dir . "zero_backup_*.sql");
    
    foreach ($files as $file) {
        $filename = basename($file);
        $info = [
            'filename' => $filename,
            'size' => filesize($file),
            'created_at' => date('Y-m-d H:i:s', filemtime($file)),
            'type' => 'unknown'
        ];
        
        // محاولة قراءة ملف المعلومات
        $info_file = str_replace('.sql', '.json', $filename);
        $info_file = str_replace('zero_backup_', 'info_', $info_file);
        $info_path = $backup_dir . $info_file;
        
        if (file_exists($info_path)) {
            $info_data = json_decode(file_get_contents($info_path), true);
            if ($info_data) {
                $info = array_merge($info, $info_data);
            }
        }
        
        $backups[] = $info;
    }
    
    // ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
    usort($backups, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });
    
    return $backups;
}

// وظيفة إنشاء نسخة احتياطية من قاعدة البيانات
function createDatabaseBackup($conn, $type = 'full') {
    $backup_content = "-- نسخة احتياطية من قاعدة بيانات Zero\n";
    $backup_content .= "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n";
    $backup_content .= "-- نوع النسخة: $type\n\n";
    
    $backup_content .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
    
    // تحديد الجداول حسب نوع النسخة
    if ($type === 'full') {
        $tables_query = "SHOW TABLES";
    } else {
        // نسخة البيانات فقط (بدون المعاملات)
        $tables_query = "SHOW TABLES";
        $exclude_tables = ['sales', 'sale_items', 'purchases', 'purchase_items', 'expenses', 'treasury'];
    }
    
    $tables_result = $conn->query($tables_query);
    
    while ($table = $tables_result->fetch_array()) {
        $table_name = $table[0];
        
        // تخطي الجداول المستبعدة في النسخة المحدودة
        if ($type !== 'full' && isset($exclude_tables) && in_array($table_name, $exclude_tables)) {
            continue;
        }
        
        // إنشاء هيكل الجدول
        $create_table_result = $conn->query("SHOW CREATE TABLE `$table_name`");
        $create_table = $create_table_result->fetch_array();
        
        $backup_content .= "-- هيكل الجدول `$table_name`\n";
        $backup_content .= "DROP TABLE IF EXISTS `$table_name`;\n";
        $backup_content .= $create_table[1] . ";\n\n";
        
        // إضافة البيانات
        $data_result = $conn->query("SELECT * FROM `$table_name`");
        if ($data_result->num_rows > 0) {
            $backup_content .= "-- بيانات الجدول `$table_name`\n";
            
            while ($row = $data_result->fetch_assoc()) {
                $backup_content .= "INSERT INTO `$table_name` VALUES (";
                $values = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $values[] = 'NULL';
                    } else {
                        $values[] = "'" . $conn->real_escape_string($value) . "'";
                    }
                }
                $backup_content .= implode(', ', $values) . ");\n";
            }
            $backup_content .= "\n";
        }
    }
    
    $backup_content .= "SET FOREIGN_KEY_CHECKS=1;\n";
    
    return $backup_content;
}

// وظيفة إنشاء نسخة احتياطية من الملفات
function createFilesBackup($timestamp) {
    $files_backup_name = "zero_files_{$timestamp}.zip";
    $files_backup_path = "../../backups/" . $files_backup_name;
    
    if (class_exists('ZipArchive')) {
        $zip = new ZipArchive();
        if ($zip->open($files_backup_path, ZipArchive::CREATE) === TRUE) {
            // إضافة ملفات المشروع المهمة
            $files_to_backup = [
                '../../assets/',
                '../../uploads/',
                '../../config/',
                '../../includes/'
            ];
            
            foreach ($files_to_backup as $dir) {
                if (is_dir($dir)) {
                    addDirectoryToZip($zip, $dir, basename($dir));
                }
            }
            
            $zip->close();
            return $files_backup_name;
        }
    }
    
    return false;
}

// وظيفة مساعدة لإضافة مجلد إلى الـ ZIP
function addDirectoryToZip($zip, $dir, $zipDir) {
    if (is_dir($dir)) {
        $files = scandir($dir);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                $filePath = $dir . '/' . $file;
                $zipPath = $zipDir . '/' . $file;
                
                if (is_dir($filePath)) {
                    addDirectoryToZip($zip, $filePath, $zipPath);
                } else {
                    $zip->addFile($filePath, $zipPath);
                }
            }
        }
    }
}

$backups_list = getBackupsList($backup_dir);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .backup-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        .backup-size {
            font-size: 0.9em;
            color: #666;
        }
        .backup-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0 text-white">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2 text-white-50">إدارة النسخ الاحتياطي واستعادة البيانات</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="index.php" class="btn btn-light btn-lg me-2">
                        <i class="fas fa-cogs me-2"></i>
                        الإعدادات
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?php echo ($message_type == 'success') ? 'check-circle' : 'exclamation-circle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            
            <!-- إنشاء نسخة احتياطية جديدة -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء نسخة احتياطية
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label for="backup_type" class="form-label">نوع النسخة:</label>
                                <select class="form-select" id="backup_type" name="backup_type" required>
                                    <option value="full">نسخة كاملة (جميع البيانات)</option>
                                    <option value="data_only">البيانات الأساسية فقط</option>
                                </select>
                                <div class="form-text">
                                    النسخة الكاملة تشمل جميع المعاملات والبيانات
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="include_files" name="include_files">
                                    <label class="form-check-label" for="include_files">
                                        تضمين الملفات والصور
                                    </label>
                                </div>
                                <div class="form-text">
                                    سيتم إنشاء ملف ZIP منفصل للملفات
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" name="create_backup" class="btn btn-primary">
                                    <i class="fas fa-download me-2"></i>
                                    إنشاء النسخة الاحتياطية
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- معلومات النظام -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="border-end">
                                    <h4 class="text-primary"><?php echo count($backups_list); ?></h4>
                                    <small>نسخة احتياطية</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <h4 class="text-success">
                                    <?php 
                                    $total_size = 0;
                                    foreach ($backups_list as $backup) {
                                        $total_size += $backup['size'];
                                    }
                                    echo formatBytes($total_size);
                                    ?>
                                </h4>
                                <small>الحجم الإجمالي</small>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="small text-muted">
                            <div><strong>قاعدة البيانات:</strong> <?php echo $db_name; ?></div>
                            <div><strong>آخر نسخة:</strong> 
                                <?php 
                                if (!empty($backups_list)) {
                                    echo $backups_list[0]['created_at'];
                                } else {
                                    echo "لا توجد نسخ";
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- قائمة النسخ الاحتياطية -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            النسخ الاحتياطية المتاحة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($backups_list)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد نسخ احتياطية</h5>
                                <p class="text-muted">قم بإنشاء أول نسخة احتياطية من النموذج الجانبي</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($backups_list as $backup): ?>
                                <div class="backup-item">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <h6 class="mb-1">
                                                <i class="fas fa-database me-2 text-primary"></i>
                                                <?php echo htmlspecialchars($backup['filename']); ?>
                                            </h6>
                                            <div class="backup-size">
                                                <span class="badge bg-<?php echo ($backup['type'] == 'full') ? 'primary' : 'secondary'; ?>">
                                                    <?php echo ($backup['type'] == 'full') ? 'نسخة كاملة' : 'بيانات أساسية'; ?>
                                                </span>
                                                <span class="ms-2"><?php echo formatBytes($backup['size']); ?></span>
                                                <?php if (isset($backup['include_files']) && $backup['include_files']): ?>
                                                    <span class="badge bg-info ms-1">+ ملفات</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo $backup['created_at']; ?>
                                            </small>
                                            <?php if (isset($backup['created_by'])): ?>
                                                <br><small class="text-muted">
                                                    <i class="fas fa-user me-1"></i>
                                                    <?php echo htmlspecialchars($backup['created_by']); ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="backup-actions">
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="backup_file" value="<?php echo $backup['filename']; ?>">
                                                    <button type="submit" name="restore_backup" class="btn btn-success btn-sm" 
                                                            onclick="return confirm('هل أنت متأكد من استعادة هذه النسخة؟\n\nسيتم استبدال البيانات الحالية!')">
                                                        <i class="fas fa-upload me-1"></i>استعادة
                                                    </button>
                                                </form>
                                                
                                                <a href="../../backups/<?php echo $backup['filename']; ?>" 
                                                   class="btn btn-info btn-sm" download>
                                                    <i class="fas fa-download me-1"></i>تحميل
                                                </a>
                                                
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="backup_file" value="<?php echo $backup['filename']; ?>">
                                                    <button type="submit" name="delete_backup" class="btn btn-danger btn-sm" 
                                                            onclick="return confirm('هل أنت متأكد من حذف هذه النسخة؟')">
                                                        <i class="fas fa-trash me-1"></i>حذف
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحديث الصفحة كل 30 ثانية لعرض النسخ الجديدة
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>

</body>
</html>

<?php
// وظيفة تنسيق حجم الملف
function formatBytes($size, $precision = 2) {
    if ($size <= 0) {
        return '0 B';
    }

    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    $index = floor($base);

    // التأكد من أن المؤشر ضمن النطاق المسموح
    if ($index < 0) $index = 0;
    if ($index >= count($suffixes)) $index = count($suffixes) - 1;

    return round(pow(1024, $base - $index), $precision) . ' ' . $suffixes[$index];
}
?>
