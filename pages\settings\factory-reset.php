<?php
/**
 * صفحة إعادة ضبط المصنع
 * Factory Reset Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";
require_once "../../includes/session-helper.php";

// التحقق من تسجيل الدخول وصلاحية المدير
requireAdmin();

$page_title = "إعادة ضبط المصنع";
$page_icon = "fas fa-exclamation-triangle";

$message = '';
$message_type = '';

// معالجة إعادة ضبط المصنع المتطور
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['factory_reset'])) {
    try {
        $confirmation_text = clean($conn, $_POST['confirmation_text']);
        $admin_password = $_POST['admin_password'];

        // التحقق من النص التأكيدي
        if ($confirmation_text !== 'RESET ZERO SYSTEM') {
            throw new Exception("النص التأكيدي غير صحيح");
        }

        // التحقق من كلمة مرور المدير
        $admin_check = $conn->prepare("SELECT password FROM users WHERE id = ? AND role = 'admin'");
        $admin_check->bind_param("i", $_SESSION['user_id']);
        $admin_check->execute();
        $admin_result = $admin_check->get_result();

        if ($admin_result->num_rows === 0) {
            throw new Exception("خطأ في التحقق من صلاحية المدير");
        }

        $admin_data = $admin_result->fetch_assoc();
        if (!password_verify($admin_password, $admin_data['password'])) {
            throw new Exception("كلمة مرور المدير غير صحيحة");
        }

        // بدء المعاملة
        $conn->begin_transaction();

        $reset_summary = [];

        // الخيارات المختارة
        $reset_sales = isset($_POST['reset_sales']);
        $reset_purchases = isset($_POST['reset_purchases']);
        $reset_expenses = isset($_POST['reset_expenses']);
        $reset_treasury = isset($_POST['reset_treasury']);
        $reset_products = isset($_POST['reset_products']);
        $reset_customers = isset($_POST['reset_customers']);
        $reset_suppliers = isset($_POST['reset_suppliers']);
        $reset_settings = isset($_POST['reset_settings']);

        // إعادة ضبط المبيعات
        if ($reset_sales) {
            $conn->query("DELETE FROM sale_items");
            $conn->query("DELETE FROM sales");
            $conn->query("ALTER TABLE sale_items AUTO_INCREMENT = 1");
            $conn->query("ALTER TABLE sales AUTO_INCREMENT = 1");
            $reset_summary[] = "تم مسح جميع المبيعات";
        }

        // إعادة ضبط المشتريات
        if ($reset_purchases) {
            $conn->query("DELETE FROM purchase_items");
            $conn->query("DELETE FROM purchases");
            $conn->query("ALTER TABLE purchase_items AUTO_INCREMENT = 1");
            $conn->query("ALTER TABLE purchases AUTO_INCREMENT = 1");
            $reset_summary[] = "تم مسح جميع المشتريات";
        }

        // إعادة ضبط المصروفات
        if ($reset_expenses) {
            $conn->query("DELETE FROM expenses");
            $conn->query("ALTER TABLE expenses AUTO_INCREMENT = 1");
            $reset_summary[] = "تم مسح جميع المصروفات";
        }

        // إعادة ضبط الخزينة
        if ($reset_treasury) {
            $conn->query("DELETE FROM treasury");
            $conn->query("ALTER TABLE treasury AUTO_INCREMENT = 1");
            $reset_summary[] = "تم مسح جميع معاملات الخزينة";

            // إضافة رصيد افتتاحي إذا كان مطلوباً
            if (isset($_POST['add_initial_balance']) && !empty($_POST['initial_balance'])) {
                $initial_balance = floatval($_POST['initial_balance']);
                if ($initial_balance > 0) {
                    $insert_treasury = "INSERT INTO treasury (transaction_type, amount, balance_after, description, user_id, transaction_date)
                                       VALUES ('deposit', ?, ?, 'رصيد افتتاحي - إعادة ضبط المصنع', ?, NOW())";
                    $stmt = $conn->prepare($insert_treasury);
                    $stmt->bind_param("ddi", $initial_balance, $initial_balance, $_SESSION['user_id']);
                    $stmt->execute();
                    $reset_summary[] = "تم إضافة رصيد افتتاحي: " . formatMoney($initial_balance);
                }
            }
        }

        // إعادة ضبط المنتجات
        if ($reset_products) {
            if (isset($_POST['delete_products'])) {
                // حذف المنتجات نهائياً
                $conn->query("DELETE FROM products");
                $conn->query("ALTER TABLE products AUTO_INCREMENT = 1");
                $reset_summary[] = "تم حذف جميع المنتجات نهائياً";
            } else {
                // إعادة تعيين الكميات فقط
                $conn->query("UPDATE products SET stock_quantity = 0");
                $reset_summary[] = "تم إعادة تعيين كميات جميع المنتجات إلى صفر";
            }
        }

        // إعادة ضبط العملاء
        if ($reset_customers) {
            if (isset($_POST['delete_customers'])) {
                // حذف العملاء نهائياً
                $conn->query("DELETE FROM customers");
                $conn->query("ALTER TABLE customers AUTO_INCREMENT = 1");
                $reset_summary[] = "تم حذف جميع العملاء نهائياً";
            } else {
                // إعادة تعيين الأرصدة فقط
                $conn->query("UPDATE customers SET balance = 0.00");
                $reset_summary[] = "تم إعادة تعيين أرصدة جميع العملاء إلى صفر";
            }
        }

        // إعادة ضبط الموردين
        if ($reset_suppliers) {
            if (isset($_POST['delete_suppliers'])) {
                // حذف الموردين نهائياً
                $conn->query("DELETE FROM suppliers");
                $conn->query("ALTER TABLE suppliers AUTO_INCREMENT = 1");
                $reset_summary[] = "تم حذف جميع الموردين نهائياً";
            } else {
                // إعادة تعيين الأرصدة فقط
                $conn->query("UPDATE suppliers SET balance = 0.00");
                $reset_summary[] = "تم إعادة تعيين أرصدة جميع الموردين إلى صفر";
            }
        }

        // إعادة ضبط الإعدادات
        if ($reset_settings) {
            $conn->query("DELETE FROM settings");
            $reset_summary[] = "تم مسح جميع إعدادات النظام";
        }

        $conn->commit();

        if (empty($reset_summary)) {
            $message = "لم يتم اختيار أي عناصر لإعادة الضبط";
            $message_type = 'warning';
        } else {
            $message = "تم إعادة ضبط المصنع بنجاح:<br>" . implode("<br>", $reset_summary);
            $message_type = 'success';
        }

    } catch (Exception $e) {
        $conn->rollback();
        $message = "خطأ في إعادة ضبط المصنع: " . $e->getMessage();
        $message_type = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .danger-card {
            background: white;
            border: 3px solid #dc3545;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .danger-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .confirmation-input {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            text-align: center;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">مسح جميع البيانات وإعادة تعيين النظام</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="./index.php" class="btn btn-light btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للإعدادات
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg ms-2">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?php echo ($message_type == 'success') ? 'check-circle' : (($message_type == 'warning') ? 'exclamation-triangle' : 'exclamation-circle'); ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                
                <!-- تحذير خطر -->
                <div class="danger-box">
                    <h3 class="text-danger mb-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        إعادة ضبط المصنع المتطورة
                    </h3>
                    <p class="mb-0">
                        <strong>اختر العناصر التي تريد إعادة ضبطها. جميع الخيارات اختيارية عدا بيانات المستخدمين التي ستبقى محفوظة.</strong>
                    </p>
                </div>

                <!-- نموذج إعادة ضبط المصنع -->
                <div class="danger-card">
                    <h4 class="text-primary mb-4">
                        <i class="fas fa-cogs me-2"></i>
                        خيارات إعادة الضبط
                    </h4>

                    <form method="POST" id="resetForm">

                        <!-- خيارات المعاملات المالية -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5 class="text-warning mb-3">
                                    <i class="fas fa-money-bill-wave me-2"></i>
                                    المعاملات المالية
                                </h5>

                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="reset_sales" name="reset_sales">
                                    <label class="form-check-label" for="reset_sales">
                                        <i class="fas fa-shopping-cart text-success me-2"></i>
                                        مسح جميع المبيعات
                                    </label>
                                </div>

                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="reset_purchases" name="reset_purchases">
                                    <label class="form-check-label" for="reset_purchases">
                                        <i class="fas fa-truck text-primary me-2"></i>
                                        مسح جميع المشتريات
                                    </label>
                                </div>

                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="reset_expenses" name="reset_expenses">
                                    <label class="form-check-label" for="reset_expenses">
                                        <i class="fas fa-receipt text-danger me-2"></i>
                                        مسح جميع المصروفات
                                    </label>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="reset_treasury" name="reset_treasury">
                                    <label class="form-check-label" for="reset_treasury">
                                        <i class="fas fa-cash-register text-warning me-2"></i>
                                        مسح معاملات الخزينة
                                    </label>
                                </div>

                                <!-- خيارات الرصيد الافتتاحي -->
                                <div class="border rounded p-3 bg-light" id="initial_balance_section" style="display: none;">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="add_initial_balance" name="add_initial_balance">
                                        <label class="form-check-label" for="add_initial_balance">
                                            إضافة رصيد افتتاحي للخزينة
                                        </label>
                                    </div>
                                    <div class="mb-2" id="initial_balance_input" style="display: none;">
                                        <label for="initial_balance" class="form-label">المبلغ:</label>
                                        <input type="number" class="form-control" id="initial_balance" name="initial_balance"
                                               step="0.01" min="0" placeholder="0.00">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h5 class="text-info mb-3">
                                    <i class="fas fa-database me-2"></i>
                                    البيانات الأساسية
                                </h5>

                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="reset_products" name="reset_products">
                                    <label class="form-check-label" for="reset_products">
                                        <i class="fas fa-boxes text-secondary me-2"></i>
                                        إعادة ضبط المنتجات
                                    </label>
                                </div>
                                <div class="ms-4 mb-3" id="products_options" style="display: none;">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="products_action" id="reset_quantities" value="reset_quantities" checked>
                                        <label class="form-check-label" for="reset_quantities">
                                            إعادة تعيين الكميات إلى صفر فقط
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="delete_products" name="delete_products">
                                        <label class="form-check-label text-danger" for="delete_products">
                                            حذف جميع المنتجات نهائياً
                                        </label>
                                    </div>
                                </div>

                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="reset_customers" name="reset_customers">
                                    <label class="form-check-label" for="reset_customers">
                                        <i class="fas fa-users text-info me-2"></i>
                                        إعادة ضبط العملاء
                                    </label>
                                </div>
                                <div class="ms-4 mb-3" id="customers_options" style="display: none;">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="customers_action" id="reset_balances" value="reset_balances" checked>
                                        <label class="form-check-label" for="reset_balances">
                                            إعادة تعيين الأرصدة إلى صفر فقط
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="delete_customers" name="delete_customers">
                                        <label class="form-check-label text-danger" for="delete_customers">
                                            حذف جميع العملاء نهائياً
                                        </label>
                                    </div>
                                </div>

                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="reset_suppliers" name="reset_suppliers">
                                    <label class="form-check-label" for="reset_suppliers">
                                        <i class="fas fa-industry text-dark me-2"></i>
                                        إعادة ضبط الموردين
                                    </label>
                                </div>
                                <div class="ms-4 mb-3" id="suppliers_options" style="display: none;">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="suppliers_action" id="reset_supplier_balances" value="reset_balances" checked>
                                        <label class="form-check-label" for="reset_supplier_balances">
                                            إعادة تعيين الأرصدة إلى صفر فقط
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="delete_suppliers" name="delete_suppliers">
                                        <label class="form-check-label text-danger" for="delete_suppliers">
                                            حذف جميع الموردين نهائياً
                                        </label>
                                    </div>
                                </div>

                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="reset_settings" name="reset_settings">
                                    <label class="form-check-label" for="reset_settings">
                                        <i class="fas fa-cog text-secondary me-2"></i>
                                        مسح إعدادات النظام
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات مهمة -->
                        <div class="alert alert-success mb-4">
                            <h6 class="text-success mb-2">
                                <i class="fas fa-shield-alt me-2"></i>
                                البيانات المحمية (لن يتم مسحها أبداً):
                            </h6>
                            <ul class="mb-0">
                                <li><strong>بيانات المستخدمين:</strong> الأسماء، أسماء المستخدمين، كلمات المرور، الأدوار</li>
                                <li><strong>هيكل قاعدة البيانات:</strong> الجداول والعلاقات</li>
                                <li><strong>ملفات النظام:</strong> الكود والإعدادات الأساسية</li>
                            </ul>
                        </div>

                        <!-- قسم التأكيد -->
                        <div class="border-top pt-4">
                            <h5 class="text-danger mb-3">
                                <i class="fas fa-key me-2"></i>
                                تأكيد العملية
                            </h5>

                            <!-- النص التأكيدي -->
                            <div class="mb-3">
                                <label for="confirmation_text" class="form-label text-danger">
                                    <strong>اكتب النص التالي بالضبط للتأكيد:</strong>
                                    <code>RESET ZERO SYSTEM</code>
                                </label>
                                <input type="text" class="form-control confirmation-input" id="confirmation_text"
                                       name="confirmation_text" required placeholder="RESET ZERO SYSTEM">
                            </div>

                            <!-- كلمة مرور المدير -->
                            <div class="mb-4">
                                <label for="admin_password" class="form-label text-danger">
                                    <strong>كلمة مرور المدير للتأكيد:</strong>
                                </label>
                                <input type="password" class="form-control" id="admin_password"
                                       name="admin_password" required placeholder="أدخل كلمة مرور المدير">
                            </div>

                            <!-- أزرار التحكم -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <a href="index.php" class="btn btn-secondary btn-lg me-md-2">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                                <button type="submit" name="factory_reset" class="btn btn-danger btn-lg" id="resetButton" disabled>
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    تنفيذ إعادة الضبط
                                </button>
                            </div>
                        </div>

                    </form>
                </div>
                
                <!-- معلومات إضافية -->
                <div class="warning-box">
                    <h5 class="text-warning">
                        <i class="fas fa-lightbulb me-2"></i>
                        ملاحظات مهمة:
                    </h5>
                    <ul>
                        <li>تأكد من إنشاء نسخة احتياطية قبل المتابعة</li>
                        <li>هذه العملية لا يمكن التراجع عنها</li>
                        <li>سيتم إضافة رصيد افتتاحي 10,000 ريال للخزينة</li>
                        <li>يمكنك البدء من جديد بعد إعادة الضبط</li>
                    </ul>
                </div>
            </div>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إدارة عرض خيارات الرصيد الافتتاحي
        document.getElementById('reset_treasury').addEventListener('change', function() {
            const initialBalanceSection = document.getElementById('initial_balance_section');
            if (this.checked) {
                initialBalanceSection.style.display = 'block';
            } else {
                initialBalanceSection.style.display = 'none';
                document.getElementById('add_initial_balance').checked = false;
                document.getElementById('initial_balance_input').style.display = 'none';
            }
        });

        document.getElementById('add_initial_balance').addEventListener('change', function() {
            const initialBalanceInput = document.getElementById('initial_balance_input');
            if (this.checked) {
                initialBalanceInput.style.display = 'block';
            } else {
                initialBalanceInput.style.display = 'none';
            }
        });

        // إدارة عرض خيارات المنتجات
        document.getElementById('reset_products').addEventListener('change', function() {
            const productsOptions = document.getElementById('products_options');
            if (this.checked) {
                productsOptions.style.display = 'block';
            } else {
                productsOptions.style.display = 'none';
            }
        });

        // إدارة عرض خيارات العملاء
        document.getElementById('reset_customers').addEventListener('change', function() {
            const customersOptions = document.getElementById('customers_options');
            if (this.checked) {
                customersOptions.style.display = 'block';
            } else {
                customersOptions.style.display = 'none';
            }
        });

        // إدارة عرض خيارات الموردين
        document.getElementById('reset_suppliers').addEventListener('change', function() {
            const suppliersOptions = document.getElementById('suppliers_options');
            if (this.checked) {
                suppliersOptions.style.display = 'block';
            } else {
                suppliersOptions.style.display = 'none';
            }
        });

        // تفعيل زر إعادة الضبط
        function checkForm() {
            const confirmationText = document.getElementById('confirmation_text').value;
            const adminPassword = document.getElementById('admin_password').value;
            const resetButton = document.getElementById('resetButton');

            // التحقق من اختيار خيار واحد على الأقل
            const mainCheckboxes = ['reset_sales', 'reset_purchases', 'reset_expenses', 'reset_treasury', 'reset_products', 'reset_customers', 'reset_suppliers', 'reset_settings'];
            let hasSelection = false;
            mainCheckboxes.forEach(id => {
                if (document.getElementById(id).checked) {
                    hasSelection = true;
                }
            });

            if (confirmationText === 'RESET ZERO SYSTEM' && adminPassword.length > 0 && hasSelection) {
                resetButton.disabled = false;
                resetButton.classList.remove('btn-secondary');
                resetButton.classList.add('btn-danger');
            } else {
                resetButton.disabled = true;
                resetButton.classList.remove('btn-danger');
                resetButton.classList.add('btn-secondary');
            }
        }

        // إضافة مستمعين للأحداث
        document.getElementById('confirmation_text').addEventListener('input', checkForm);
        document.getElementById('admin_password').addEventListener('input', checkForm);

        // إضافة مستمعين لجميع الخيارات الرئيسية
        const mainCheckboxes = ['reset_sales', 'reset_purchases', 'reset_expenses', 'reset_treasury', 'reset_products', 'reset_customers', 'reset_suppliers', 'reset_settings'];
        mainCheckboxes.forEach(id => {
            document.getElementById(id).addEventListener('change', checkForm);
        });

        // تأكيد إضافي قبل الإرسال
        document.getElementById('resetForm').addEventListener('submit', function(e) {
            const confirmationText = document.getElementById('confirmation_text').value;
            const adminPassword = document.getElementById('admin_password').value;

            if (confirmationText !== 'RESET ZERO SYSTEM') {
                e.preventDefault();
                alert('النص التأكيدي غير صحيح');
                return false;
            }

            if (!adminPassword) {
                e.preventDefault();
                alert('كلمة مرور المدير مطلوبة');
                return false;
            }

            // جمع الخيارات المختارة
            const selectedOptions = [];
            if (document.getElementById('reset_sales').checked) selectedOptions.push('المبيعات');
            if (document.getElementById('reset_purchases').checked) selectedOptions.push('المشتريات');
            if (document.getElementById('reset_expenses').checked) selectedOptions.push('المصروفات');
            if (document.getElementById('reset_treasury').checked) selectedOptions.push('الخزينة');
            if (document.getElementById('reset_products').checked) selectedOptions.push('المنتجات');
            if (document.getElementById('reset_customers').checked) selectedOptions.push('العملاء');
            if (document.getElementById('reset_suppliers').checked) selectedOptions.push('الموردين');
            if (document.getElementById('reset_settings').checked) selectedOptions.push('الإعدادات');

            if (selectedOptions.length === 0) {
                alert('يرجى اختيار عنصر واحد على الأقل لإعادة الضبط');
                e.preventDefault();
                return false;
            }

            const optionsText = selectedOptions.join('، ');

            if (!confirm(`هل أنت متأكد من إعادة ضبط العناصر التالية؟\n\n${optionsText}\n\nهذه العملية لا يمكن التراجع عنها!`)) {
                e.preventDefault();
                return false;
            }

            // تأكيد ثاني للعمليات الخطيرة
            const dangerousOperations = [];
            if (document.getElementById('delete_products') && document.getElementById('delete_products').checked) {
                dangerousOperations.push('حذف جميع المنتجات نهائياً');
            }
            if (document.getElementById('delete_customers') && document.getElementById('delete_customers').checked) {
                dangerousOperations.push('حذف جميع العملاء نهائياً');
            }
            if (document.getElementById('delete_suppliers') && document.getElementById('delete_suppliers').checked) {
                dangerousOperations.push('حذف جميع الموردين نهائياً');
            }

            if (dangerousOperations.length > 0) {
                const dangerText = dangerousOperations.join('\n');
                if (!confirm(`تحذير: ستقوم بالعمليات التالية الخطيرة:\n\n${dangerText}\n\nهل أنت متأكد تماماً؟`)) {
                    e.preventDefault();
                    return false;
                }
            }
        });

        // تشغيل فحص النموذج عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', checkForm);
    </script>

</body>
</html>
