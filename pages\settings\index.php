<?php
/**
 * صفحة الإعدادات الرئيسية
 * Settings Main Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";
require_once "../../includes/session-helper.php";

// التحقق من تسجيل الدخول
requireLogin();

$page_title = "إعدادات النظام";
$page_icon = "fas fa-cogs";

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    try {
        $settings_to_update = [
            'store_name' => clean($conn, $_POST['store_name']),
            'store_address' => clean($conn, $_POST['store_address']),
            'store_phone' => clean($conn, $_POST['store_phone']),
            'store_email' => clean($conn, $_POST['store_email']),
            'store_currency' => clean($conn, $_POST['store_currency']),
            'tax_rate' => floatval($_POST['tax_rate']),
            'default_payment_method' => clean($conn, $_POST['default_payment_method']),
            'low_stock_threshold' => intval($_POST['low_stock_threshold']),
            'backup_frequency' => clean($conn, $_POST['backup_frequency']),
            'receipt_footer' => clean($conn, $_POST['receipt_footer']),
            'enable_notifications' => isset($_POST['enable_notifications']) ? 1 : 0,
            'enable_barcode_scanner' => isset($_POST['enable_barcode_scanner']) ? 1 : 0,
            'auto_backup' => isset($_POST['auto_backup']) ? 1 : 0
        ];
        
        foreach ($settings_to_update as $key => $value) {
            // التحقق من وجود الإعداد
            $check_query = "SELECT id FROM settings WHERE setting_key = ?";
            $check_stmt = $conn->prepare($check_query);
            $check_stmt->bind_param("s", $key);
            $check_stmt->execute();
            $exists = $check_stmt->get_result()->num_rows > 0;
            
            if ($exists) {
                // تحديث الإعداد الموجود
                $update_query = "UPDATE settings SET setting_value = ? WHERE setting_key = ?";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bind_param("ss", $value, $key);
                $update_stmt->execute();
            } else {
                // إضافة إعداد جديد
                $insert_query = "INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)";
                $insert_stmt = $conn->prepare($insert_query);
                $insert_stmt->bind_param("ss", $key, $value);
                $insert_stmt->execute();
            }
        }
        
        $_SESSION['success_message'] = "تم حفظ الإعدادات بنجاح";
        header("Location: index.php");
        exit();
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = "خطأ في حفظ الإعدادات: " . $e->getMessage();
    }
}

// الحصول على الإعدادات الحالية
$current_settings = [];
$settings_query = "SELECT setting_key, setting_value FROM settings";
$settings_result = $conn->query($settings_query);

while ($setting = $settings_result->fetch_assoc()) {
    $current_settings[$setting['setting_key']] = $setting['setting_value'];
}

// الإعدادات الافتراضية
$default_settings = [
    'store_name' => 'متجر Zero',
    'store_address' => '',
    'store_phone' => '',
    'store_email' => '',
    'store_currency' => 'ريال',
    'tax_rate' => 15,
    'default_payment_method' => 'cash',
    'low_stock_threshold' => 5,
    'backup_frequency' => 'weekly',
    'receipt_footer' => 'شكراً لتعاملكم معنا',
    'enable_notifications' => 1,
    'enable_barcode_scanner' => 0,
    'auto_backup' => 1
];

// دمج الإعدادات الحالية مع الافتراضية
foreach ($default_settings as $key => $default_value) {
    if (!isset($current_settings[$key])) {
        $current_settings[$key] = $default_value;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .settings-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .settings-section {
            border-bottom: 1px solid #eee;
            padding-bottom: 2rem;
            margin-bottom: 2rem;
        }
        .settings-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .required {
            color: #dc3545;
        }
        .info-box {
            background: #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">إدارة إعدادات المتجر والنظام</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="users.php" class="btn btn-info btn-lg me-2">
                        <i class="fas fa-users-cog me-2"></i>
                        المستخدمين
                    </a>
                    <a href="backup.php" class="btn btn-light btn-lg me-2">
                        <i class="fas fa-download me-2"></i>
                        نسخ احتياطي
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- قسم إدارة النظام -->
        <?php if (hasPermission('admin') || getCurrentUserRole() === 'admin'): ?>
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <i class="fas fa-users-cog fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">إدارة المستخدمين</h5>
                            <p class="card-text">إضافة وتعديل وحذف المستخدمين وإدارة الأدوار والصلاحيات</p>
                            <a href="users.php" class="btn btn-primary">
                                <i class="fas fa-users me-2"></i>إدارة المستخدمين
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <i class="fas fa-download fa-3x text-success mb-3"></i>
                            <h5 class="card-title">النسخ الاحتياطي</h5>
                            <p class="card-text">إنشاء واستعادة النسخ الاحتياطية لحماية بياناتك</p>
                            <a href="backup.php" class="btn btn-success">
                                <i class="fas fa-download me-2"></i>النسخ الاحتياطي
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <i class="fas fa-image fa-3x text-info mb-3"></i>
                            <h5 class="card-title">شعار المحل</h5>
                            <p class="card-text">إدارة شعار المحل الذي يظهر على الفواتير المطبوعة</p>
                            <a href="logo.php" class="btn btn-info">
                                <i class="fas fa-image me-2"></i>إدارة الشعار
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">إعادة ضبط المصنع</h5>
                            <p class="card-text">مسح جميع البيانات وإعادة النظام لحالته الأولى</p>
                            <a href="factory-reset.php" class="btn btn-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>إعادة ضبط المصنع
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <form method="POST" id="settingsForm">
            <div class="row">
                <div class="col-lg-8">
                    
                    <!-- إعدادات المتجر -->
                    <div class="settings-card">
                        <div class="settings-section">
                            <h4 class="mb-4">
                                <i class="fas fa-store me-2 text-primary"></i>
                                إعدادات المتجر
                            </h4>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="store_name" class="form-label">اسم المتجر <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="store_name" name="store_name" 
                                           value="<?php echo htmlspecialchars($current_settings['store_name']); ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="store_phone" class="form-label">هاتف المتجر</label>
                                    <input type="tel" class="form-control" id="store_phone" name="store_phone" 
                                           value="<?php echo htmlspecialchars($current_settings['store_phone']); ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="store_email" class="form-label">بريد المتجر الإلكتروني</label>
                                    <input type="email" class="form-control" id="store_email" name="store_email" 
                                           value="<?php echo htmlspecialchars($current_settings['store_email']); ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="store_currency" class="form-label">العملة <span class="required">*</span></label>
                                    <select class="form-select" id="store_currency" name="store_currency" required>
                                        <option value="ريال" <?php echo ($current_settings['store_currency'] == 'ريال') ? 'selected' : ''; ?>>ريال سعودي</option>
                                        <option value="درهم" <?php echo ($current_settings['store_currency'] == 'درهم') ? 'selected' : ''; ?>>درهم إماراتي</option>
                                        <option value="دينار" <?php echo ($current_settings['store_currency'] == 'دينار') ? 'selected' : ''; ?>>دينار كويتي</option>
                                        <option value="جنيه" <?php echo ($current_settings['store_currency'] == 'جنيه') ? 'selected' : ''; ?>>جنيه مصري</option>
                                        <option value="دولار" <?php echo ($current_settings['store_currency'] == 'دولار') ? 'selected' : ''; ?>>دولار أمريكي</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-12 mb-3">
                                    <label for="store_address" class="form-label">عنوان المتجر</label>
                                    <textarea class="form-control" id="store_address" name="store_address" rows="2"><?php echo htmlspecialchars($current_settings['store_address']); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات المبيعات -->
                        <div class="settings-section">
                            <h4 class="mb-4">
                                <i class="fas fa-shopping-cart me-2 text-success"></i>
                                إعدادات المبيعات
                            </h4>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="tax_rate" class="form-label">معدل الضريبة (%)</label>
                                    <input type="number" class="form-control" id="tax_rate" name="tax_rate" 
                                           step="0.01" min="0" max="100" 
                                           value="<?php echo $current_settings['tax_rate']; ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="default_payment_method" class="form-label">طريقة الدفع الافتراضية</label>
                                    <select class="form-select" id="default_payment_method" name="default_payment_method">
                                        <option value="cash" <?php echo ($current_settings['default_payment_method'] == 'cash') ? 'selected' : ''; ?>>نقدي</option>
                                        <option value="card" <?php echo ($current_settings['default_payment_method'] == 'card') ? 'selected' : ''; ?>>بطاقة</option>
                                        <option value="bank_transfer" <?php echo ($current_settings['default_payment_method'] == 'bank_transfer') ? 'selected' : ''; ?>>تحويل بنكي</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-12 mb-3">
                                    <label for="receipt_footer" class="form-label">تذييل الفاتورة</label>
                                    <textarea class="form-control" id="receipt_footer" name="receipt_footer" rows="2"><?php echo htmlspecialchars($current_settings['receipt_footer']); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات المخزون -->
                        <div class="settings-section">
                            <h4 class="mb-4">
                                <i class="fas fa-boxes me-2 text-warning"></i>
                                إعدادات المخزون
                            </h4>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="low_stock_threshold" class="form-label">حد التنبيه للمخزون المنخفض</label>
                                    <input type="number" class="form-control" id="low_stock_threshold" name="low_stock_threshold" 
                                           min="1" value="<?php echo $current_settings['low_stock_threshold']; ?>">
                                    <small class="text-muted">سيتم التنبيه عندما يصل المخزون لهذا الحد أو أقل</small>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="enable_barcode_scanner" name="enable_barcode_scanner" 
                                               <?php echo $current_settings['enable_barcode_scanner'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="enable_barcode_scanner">
                                            تفعيل قارئ الباركود
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات النظام -->
                        <div class="settings-section">
                            <h4 class="mb-4">
                                <i class="fas fa-cog me-2 text-info"></i>
                                إعدادات النظام
                            </h4>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="backup_frequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                    <select class="form-select" id="backup_frequency" name="backup_frequency">
                                        <option value="daily" <?php echo ($current_settings['backup_frequency'] == 'daily') ? 'selected' : ''; ?>>يومي</option>
                                        <option value="weekly" <?php echo ($current_settings['backup_frequency'] == 'weekly') ? 'selected' : ''; ?>>أسبوعي</option>
                                        <option value="monthly" <?php echo ($current_settings['backup_frequency'] == 'monthly') ? 'selected' : ''; ?>>شهري</option>
                                        <option value="manual" <?php echo ($current_settings['backup_frequency'] == 'manual') ? 'selected' : ''; ?>>يدوي فقط</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="auto_backup" name="auto_backup" 
                                               <?php echo $current_settings['auto_backup'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="auto_backup">
                                            تفعيل النسخ الاحتياطي التلقائي
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-md-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enable_notifications" name="enable_notifications" 
                                               <?php echo $current_settings['enable_notifications'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="enable_notifications">
                                            تفعيل التنبيهات (المخزون المنخفض، المدفوعات المتأخرة، إلخ)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات النظام -->
                <div class="col-lg-4">
                    <div class="settings-card">
                        <h4 class="mb-4">
                            <i class="fas fa-info-circle me-2 text-secondary"></i>
                            معلومات النظام
                        </h4>
                        
                        <div class="info-box">
                            <strong>نظام Zero</strong><br>
                            <small class="text-muted">الإصدار 1.0</small>
                        </div>
                        
                        <div class="info-box">
                            <strong>قاعدة البيانات:</strong><br>
                            <small class="text-muted">MySQL</small>
                        </div>
                        
                        <div class="info-box">
                            <strong>آخر نسخة احتياطية:</strong><br>
                            <small class="text-muted">
                                <?php
                                // البحث عن آخر نسخة احتياطية
                                $backup_dir = "../../backups/";
                                if (is_dir($backup_dir)) {
                                    $files = glob($backup_dir . "*.sql");
                                    if (!empty($files)) {
                                        $latest_backup = max($files);
                                        echo date('Y-m-d H:i', filemtime($latest_backup));
                                    } else {
                                        echo "لم يتم إنشاء نسخة احتياطية بعد";
                                    }
                                } else {
                                    echo "لم يتم إنشاء نسخة احتياطية بعد";
                                }
                                ?>
                            </small>
                        </div>
                        
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" name="save_settings" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                            <a href="backup.php" class="btn btn-outline-secondary">
                                <i class="fas fa-download me-2"></i>إنشاء نسخة احتياطية
                            </a>
                            <button type="button" class="btn btn-outline-danger" onclick="goToFactoryReset()">
                                <i class="fas fa-exclamation-triangle me-2"></i>إعادة ضبط المصنع
                            </button>
                            <a href="../../index.php" class="btn btn-outline-secondary">
                                <i class="fas fa-home me-2"></i>العودة للرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // التحقق من صحة النموذج
        document.getElementById('settingsForm').addEventListener('submit', function(e) {
            const storeName = document.getElementById('store_name').value.trim();
            const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;

            if (!storeName) {
                e.preventDefault();
                alert('اسم المتجر مطلوب');
                return false;
            }

            if (taxRate < 0 || taxRate > 100) {
                e.preventDefault();
                alert('معدل الضريبة يجب أن يكون بين 0 و 100');
                return false;
            }
        });

        // وظيفة الانتقال لإعادة ضبط المصنع
        function goToFactoryReset() {
            const confirmed = confirm('هل تريد الانتقال لصفحة إعادة ضبط المصنع؟\n\nتحذير: هذه الصفحة تحتوي على إعدادات خطيرة يمكنها مسح جميع البيانات!');
            if (confirmed) {
                console.log('الانتقال لصفحة إعادة ضبط المصنع...');
                // استخدام عدة طرق للتأكد من الانتقال
                try {
                    window.location.href = 'factory-reset.php';
                } catch (e) {
                    console.error('خطأ في الانتقال:', e);
                    // طريقة بديلة
                    window.open('factory-reset.php', '_self');
                }
            }
        }

        // التأكد من تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('صفحة الإعدادات تم تحميلها بنجاح');
            console.log('المسار الحالي:', window.location.pathname);
            console.log('URL الكامل:', window.location.href);
        });
    </script>

</body>
</html>
