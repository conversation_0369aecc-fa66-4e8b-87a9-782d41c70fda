<?php
/**
 * إدارة شعار المحل
 * Store Logo Management
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";
require_once "../../includes/session-helper.php";

// التحقق من تسجيل الدخول وصلاحية المدير أو المدير
if (!hasPermission('admin') && !hasPermission('manager')) {
    $_SESSION['error_message'] = "غير مسموح لك بالوصول لهذه الصفحة";
    header("Location: ../../index.php");
    exit();
}

$page_title = "إدارة شعار المحل";
$page_icon = "fas fa-image";

$message = '';
$message_type = '';

// إنشاء مجلد الشعارات إذا لم يكن موجوداً
$logo_dir = "../../uploads/logo/";
if (!is_dir($logo_dir)) {
    mkdir($logo_dir, 0755, true);
}

// معالجة رفع الشعار
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_logo'])) {
    try {
        if (!isset($_FILES['logo_file']) || $_FILES['logo_file']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception("يرجى اختيار ملف صورة صحيح");
        }
        
        $file = $_FILES['logo_file'];
        $file_name = $file['name'];
        $file_tmp = $file['tmp_name'];
        $file_size = $file['size'];
        $file_error = $file['error'];
        
        // التحقق من نوع الملف بطريقة مرنة ومتدرجة
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];

        // التحقق من امتداد الملف أولاً
        $file_extension = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        if (!in_array($file_extension, $allowed_extensions)) {
            throw new Exception("امتداد الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF). الامتداد المكتشف: " . $file_extension);
        }

        // التحقق من وجود الملف المؤقت
        if (!file_exists($file_tmp) || !is_readable($file_tmp)) {
            throw new Exception("لا يمكن قراءة الملف المرفوع. يرجى المحاولة مرة أخرى");
        }

        // محاولة التحقق من صحة الصورة بطرق متعددة
        $is_valid_image = false;
        $image_info = null;
        $error_details = [];

        // الطريقة الأولى: getimagesize
        $image_info = @getimagesize($file_tmp);
        if ($image_info !== false) {
            $is_valid_image = true;

            // التحقق من نوع MIME
            $allowed_mime_types = ['image/jpeg', 'image/png', 'image/gif'];
            if (!in_array($image_info['mime'], $allowed_mime_types)) {
                throw new Exception("نوع الصورة غير مدعوم. النوع المكتشف: " . $image_info['mime']);
            }

            // التحقق من الأبعاد
            if ($image_info[0] > 2000 || $image_info[1] > 2000) {
                throw new Exception("أبعاد الصورة كبيرة جداً. الحد الأقصى 2000x2000 بكسل. الأبعاد الحالية: " . $image_info[0] . "x" . $image_info[1]);
            }
        } else {
            $error_details[] = "getimagesize فشل";
        }

        // الطريقة الثانية: فحص البايتات الأولى للملف
        if (!$is_valid_image) {
            $file_content = @file_get_contents($file_tmp, false, null, 0, 20);
            if ($file_content !== false) {
                // فحص توقيعات الملفات
                $signatures = [
                    'jpeg' => ["\xFF\xD8\xFF"],
                    'png' => ["\x89\x50\x4E\x47\x0D\x0A\x1A\x0A"],
                    'gif' => ["GIF87a", "GIF89a"]
                ];

                foreach ($signatures as $type => $sigs) {
                    foreach ($sigs as $sig) {
                        if (substr($file_content, 0, strlen($sig)) === $sig) {
                            $is_valid_image = true;
                            break 2;
                        }
                    }
                }

                if (!$is_valid_image) {
                    $error_details[] = "توقيع الملف لا يطابق صيغة صورة معروفة";
                }
            } else {
                $error_details[] = "فشل في قراءة محتوى الملف";
            }
        }

        // إذا فشلت جميع الطرق
        if (!$is_valid_image) {
            $error_msg = "الملف ليس صورة صحيحة. التفاصيل: " . implode(", ", $error_details);
            throw new Exception($error_msg);
        }
        
        // التحقق من حجم الملف (5MB كحد أقصى)
        if ($file_size > 5 * 1024 * 1024) {
            throw new Exception("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت");
        }
        
        // إنشاء اسم ملف فريد
        $file_extension = pathinfo($file_name, PATHINFO_EXTENSION);
        $new_file_name = 'logo_' . time() . '.' . $file_extension;
        $upload_path = $logo_dir . $new_file_name;
        
        // رفع الملف
        if (move_uploaded_file($file_tmp, $upload_path)) {
            // حذف الشعار القديم إذا كان موجوداً
            $old_logo = getSetting('store_logo', '');
            if (!empty($old_logo) && file_exists("../../" . $old_logo)) {
                unlink("../../" . $old_logo);
            }
            
            // حفظ مسار الشعار الجديد في الإعدادات
            $logo_path = "uploads/logo/" . $new_file_name;
            saveSetting('store_logo', $logo_path);
            
            $message = "تم رفع الشعار بنجاح";
            $message_type = 'success';
        } else {
            throw new Exception("فشل في رفع الملف");
        }
        
    } catch (Exception $e) {
        // إضافة معلومات تشخيصية للمطور
        $debug_info = "";
        if (isset($file)) {
            $debug_info .= "<br><small>معلومات الملف:<br>";
            $debug_info .= "الاسم: " . htmlspecialchars($file['name']) . "<br>";
            $debug_info .= "الحجم: " . number_format($file['size'] / 1024, 2) . " KB<br>";

            if (isset($file_extension)) {
                $debug_info .= "الامتداد: " . htmlspecialchars($file_extension) . "<br>";
            }

            if (function_exists('mime_content_type') && isset($file['tmp_name'])) {
                $detected_mime = @mime_content_type($file['tmp_name']);
                $debug_info .= "نوع MIME المكتشف: " . htmlspecialchars($detected_mime ?: 'غير محدد') . "<br>";
            }

            if (function_exists('finfo_open') && isset($file['tmp_name'])) {
                $finfo = @finfo_open(FILEINFO_MIME_TYPE);
                if ($finfo) {
                    $detected_finfo = @finfo_file($finfo, $file['tmp_name']);
                    $debug_info .= "نوع finfo المكتشف: " . htmlspecialchars($detected_finfo ?: 'غير محدد') . "<br>";
                    @finfo_close($finfo);
                }
            }

            if (isset($file['tmp_name'])) {
                $image_info = @getimagesize($file['tmp_name']);
                if ($image_info) {
                    $debug_info .= "معلومات الصورة: " . $image_info[0] . "x" . $image_info[1] . " بكسل<br>";
                } else {
                    $debug_info .= "فشل في قراءة معلومات الصورة<br>";
                }
            }

            $debug_info .= "</small>";
        }

        $message = "خطأ في رفع الشعار: " . $e->getMessage() . $debug_info;
        $message_type = 'danger';
    }
}

// معالجة حذف الشعار
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_logo'])) {
    try {
        $current_logo = getSetting('store_logo', '');
        
        if (!empty($current_logo)) {
            // حذف الملف
            if (file_exists("../../" . $current_logo)) {
                unlink("../../" . $current_logo);
            }
            
            // حذف من الإعدادات
            saveSetting('store_logo', '');
            
            $message = "تم حذف الشعار بنجاح";
            $message_type = 'success';
        } else {
            $message = "لا يوجد شعار لحذفه";
            $message_type = 'warning';
        }
        
    } catch (Exception $e) {
        $message = "خطأ في حذف الشعار: " . $e->getMessage();
        $message_type = 'danger';
    }
}

// الحصول على الشعار الحالي
$current_logo = getSetting('store_logo', '');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .logo-preview {
            max-width: 300px;
            max-height: 200px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
        }
        .logo-preview img {
            max-width: 100%;
            max-height: 150px;
            border-radius: 5px;
        }
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            background: #e9ecef;
            border-color: #0056b3;
        }
        .upload-area.dragover {
            background: #e3f2fd;
            border-color: #1976d2;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0 text-white">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2 text-white-50">إدارة شعار المحل الذي يظهر على الفواتير</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="index.php" class="btn btn-light btn-lg me-2">
                        <i class="fas fa-cogs me-2"></i>
                        الإعدادات
                    </a>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?php echo ($message_type == 'success') ? 'check-circle' : (($message_type == 'warning') ? 'exclamation-triangle' : 'exclamation-circle'); ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            
            <!-- الشعار الحالي -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-eye me-2"></i>
                            الشعار الحالي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="logo-preview mx-auto">
                            <?php if (!empty($current_logo) && file_exists("../../" . $current_logo)): ?>
                                <img src="../../<?php echo $current_logo; ?>" alt="شعار المحل" class="img-fluid">
                                <div class="mt-3">
                                    <p class="text-success mb-2">
                                        <i class="fas fa-check-circle me-1"></i>
                                        يوجد شعار
                                    </p>
                                    <form method="POST" style="display: inline;">
                                        <button type="submit" name="delete_logo" class="btn btn-danger btn-sm" 
                                                onclick="return confirm('هل أنت متأكد من حذف الشعار؟')">
                                            <i class="fas fa-trash me-1"></i>
                                            حذف الشعار
                                        </button>
                                    </form>
                                </div>
                            <?php else: ?>
                                <i class="fas fa-image fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا يوجد شعار حالياً</p>
                                <small class="text-muted">قم برفع شعار ليظهر على الفواتير</small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- معاينة الفاتورة -->
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-receipt me-2"></i>
                            معاينة على الفاتورة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="border rounded p-3" style="background: white; font-family: Arial;">
                            <div class="row align-items-center mb-3">
                                <div class="col-4 text-end">
                                    <div style="font-size: 12px;">
                                        <strong>متجر Zero</strong><br>
                                        الرياض، السعودية<br>
                                        0501234567
                                    </div>
                                </div>
                                <div class="col-4 text-center">
                                    <?php if (!empty($current_logo) && file_exists("../../" . $current_logo)): ?>
                                        <img src="../../<?php echo $current_logo; ?>" alt="شعار المحل" style="max-width: 80px; max-height: 60px;">
                                    <?php else: ?>
                                        <div style="border: 1px dashed #ccc; padding: 20px; font-size: 10px; color: #999;">
                                            مكان الشعار
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-4">
                                    <div style="font-size: 12px;">
                                        <strong>فاتورة مبيعة</strong><br>
                                        رقم: 2024001<br>
                                        التاريخ: <?php echo date('Y-m-d'); ?>
                                    </div>
                                </div>
                            </div>
                            <hr style="margin: 10px 0;">
                            <div style="font-size: 10px; text-align: center; color: #666;">
                                معاينة مبسطة للفاتورة
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- رفع شعار جديد -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-upload me-2"></i>
                            رفع شعار جديد
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data" id="logoForm">
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                <h5>اسحب الصورة هنا أو انقر للاختيار</h5>
                                <p class="text-muted">أنواع الملفات المدعومة: JPG, PNG, GIF</p>
                                <p class="text-muted">الحد الأقصى: 5 ميجابايت</p>
                                <input type="file" name="logo_file" id="logoFile" accept="image/jpeg,image/jpg,image/png,image/gif,.jpg,.jpeg,.png,.gif" style="display: none;" required>
                                <button type="button" class="btn btn-primary" onclick="document.getElementById('logoFile').click()">
                                    <i class="fas fa-folder-open me-2"></i>
                                    اختيار ملف
                                </button>
                            </div>
                            
                            <div id="filePreview" style="display: none;" class="mt-3">
                                <div class="border rounded p-3 bg-light">
                                    <div class="row align-items-center">
                                        <div class="col-3">
                                            <img id="previewImage" src="" alt="معاينة" class="img-fluid rounded">
                                        </div>
                                        <div class="col-6">
                                            <h6 id="fileName" class="mb-1"></h6>
                                            <small id="fileSize" class="text-muted"></small>
                                        </div>
                                        <div class="col-3 text-end">
                                            <button type="button" class="btn btn-sm btn-danger" onclick="clearFile()">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <button type="submit" name="upload_logo" class="btn btn-success w-100" id="uploadBtn" disabled>
                                    <i class="fas fa-upload me-2"></i>
                                    رفع الشعار
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- نصائح -->
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            نصائح للحصول على أفضل نتيجة
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="mb-0">
                            <li><strong>الحجم المثالي:</strong> 300x200 بكسل أو أقل</li>
                            <li><strong>الشكل:</strong> مستطيل أفقي أو مربع</li>
                            <li><strong>الخلفية:</strong> شفافة (PNG) أو بيضاء</li>
                            <li><strong>الجودة:</strong> عالية ووضوح جيد</li>
                            <li><strong>الألوان:</strong> تتناسب مع الطباعة بالأبيض والأسود</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إدارة رفع الملفات
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('logoFile');
        const filePreview = document.getElementById('filePreview');
        const previewImage = document.getElementById('previewImage');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const uploadBtn = document.getElementById('uploadBtn');
        
        // النقر على منطقة الرفع
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        // السحب والإفلات
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        });
        
        // اختيار الملف
        fileInput.addEventListener('change', handleFileSelect);
        
        function handleFileSelect() {
            const file = fileInput.files[0];
            if (file) {
                // التحقق من نوع الملف بطرق متعددة
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];

                // الحصول على امتداد الملف
                const fileExtension = file.name.split('.').pop().toLowerCase();

                // التحقق من امتداد الملف
                if (!allowedExtensions.includes(fileExtension)) {
                    alert('امتداد الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)\n\nامتداد الملف المكتشف: ' + fileExtension);
                    clearFile();
                    return;
                }

                // التحقق من نوع MIME
                if (!allowedTypes.includes(file.type)) {
                    alert('نوع الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)\n\nنوع الملف المكتشف: ' + file.type);
                    clearFile();
                    return;
                }

                // التحقق من حجم الملف
                if (file.size > 5 * 1024 * 1024) {
                    alert('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت\n\nحجم الملف: ' + formatFileSize(file.size));
                    clearFile();
                    return;
                }

                // التحقق من أن الملف صورة حقيقية
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        // الملف صورة صحيحة
                        previewImage.src = e.target.result;
                        fileName.textContent = file.name;
                        fileSize.textContent = formatFileSize(file.size) + ' - ' + img.width + 'x' + img.height + ' بكسل';
                        filePreview.style.display = 'block';
                        uploadBtn.disabled = false;
                    };
                    img.onerror = function() {
                        alert('الملف تالف أو ليس صورة صحيحة. يرجى اختيار ملف صورة صالح');
                        clearFile();
                    };
                    img.src = e.target.result;
                };
                reader.onerror = function() {
                    alert('فشل في قراءة الملف. يرجى المحاولة مرة أخرى');
                    clearFile();
                };
                reader.readAsDataURL(file);
            }
        }
        
        function clearFile() {
            fileInput.value = '';
            filePreview.style.display = 'none';
            uploadBtn.disabled = true;
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>

</body>
</html>
