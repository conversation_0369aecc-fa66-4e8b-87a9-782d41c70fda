<?php
/**
 * رفع شعار مبسط - بدون تحقق معقد
 * Simple Logo Upload - Without Complex Validation
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";
require_once "../../includes/session-helper.php";

// التحقق من تسجيل الدخول وصلاحية المدير أو المدير
if (!hasPermission('admin') && !hasPermission('manager')) {
    $_SESSION['error_message'] = "غير مسموح لك بالوصول لهذه الصفحة";
    header("Location: ../../index.php");
    exit();
}

$message = '';
$message_type = '';

// إنشاء مجلد الشعارات إذا لم يكن موجوداً
$logo_dir = "../../uploads/logo/";
if (!is_dir($logo_dir)) {
    mkdir($logo_dir, 0755, true);
}

// معالجة رفع الشعار
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_logo'])) {
    try {
        if (!isset($_FILES['logo_file']) || $_FILES['logo_file']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception("يرجى اختيار ملف صورة صحيح");
        }
        
        $file = $_FILES['logo_file'];
        $file_name = $file['name'];
        $file_tmp = $file['tmp_name'];
        $file_size = $file['size'];
        
        // التحقق البسيط من امتداد الملف
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
        $file_extension = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        
        if (!in_array($file_extension, $allowed_extensions)) {
            throw new Exception("امتداد الملف غير مدعوم. يرجى اختيار صورة (JPG, PNG, GIF)");
        }
        
        // التحقق من حجم الملف (5MB كحد أقصى)
        if ($file_size > 5 * 1024 * 1024) {
            throw new Exception("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت");
        }
        
        // إنشاء اسم ملف فريد
        $new_file_name = 'logo_' . time() . '.' . $file_extension;
        $upload_path = $logo_dir . $new_file_name;
        
        // رفع الملف
        if (move_uploaded_file($file_tmp, $upload_path)) {
            // حذف الشعار القديم إذا كان موجوداً
            $old_logo = getSetting('store_logo', '');
            if (!empty($old_logo) && file_exists("../../" . $old_logo)) {
                unlink("../../" . $old_logo);
            }
            
            // حفظ مسار الشعار الجديد في الإعدادات
            $logo_path = "uploads/logo/" . $new_file_name;
            saveSetting('store_logo', $logo_path);
            
            $message = "تم رفع الشعار بنجاح!";
            $message_type = 'success';
        } else {
            throw new Exception("فشل في رفع الملف");
        }
        
    } catch (Exception $e) {
        $message = "خطأ في رفع الشعار: " . $e->getMessage();
        $message_type = 'danger';
    }
}

// معالجة حذف الشعار
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_logo'])) {
    try {
        $current_logo = getSetting('store_logo', '');
        
        if (!empty($current_logo)) {
            // حذف الملف
            if (file_exists("../../" . $current_logo)) {
                unlink("../../" . $current_logo);
            }
            
            // حذف من الإعدادات
            saveSetting('store_logo', '');
            
            $message = "تم حذف الشعار بنجاح";
            $message_type = 'success';
        } else {
            $message = "لا يوجد شعار لحذفه";
            $message_type = 'warning';
        }
        
    } catch (Exception $e) {
        $message = "خطأ في حذف الشعار: " . $e->getMessage();
        $message_type = 'danger';
    }
}

// الحصول على الشعار الحالي
$current_logo = getSetting('store_logo', '');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رفع شعار مبسط - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .logo-preview {
            max-width: 300px;
            max-height: 200px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
        }
        .logo-preview img {
            max-width: 100%;
            max-height: 150px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-image me-3"></i>
                رفع شعار مبسط
            </h1>
            <p class="lead">رفع شعار المحل بطريقة مبسطة</p>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?php echo ($message_type == 'success') ? 'check-circle' : (($message_type == 'warning') ? 'exclamation-triangle' : 'exclamation-circle'); ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            
            <!-- الشعار الحالي -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-eye me-2"></i>
                            الشعار الحالي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="logo-preview mx-auto">
                            <?php if (!empty($current_logo) && file_exists("../../" . $current_logo)): ?>
                                <img src="../../<?php echo $current_logo; ?>" alt="شعار المحل" class="img-fluid">
                                <div class="mt-3">
                                    <p class="text-success mb-2">
                                        <i class="fas fa-check-circle me-1"></i>
                                        يوجد شعار
                                    </p>
                                    <form method="POST" style="display: inline;">
                                        <button type="submit" name="delete_logo" class="btn btn-danger btn-sm" 
                                                onclick="return confirm('هل أنت متأكد من حذف الشعار؟')">
                                            <i class="fas fa-trash me-1"></i>
                                            حذف الشعار
                                        </button>
                                    </form>
                                </div>
                            <?php else: ?>
                                <i class="fas fa-image fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا يوجد شعار حالياً</p>
                                <small class="text-muted">قم برفع شعار ليظهر على الفواتير</small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- رفع شعار جديد -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-upload me-2"></i>
                            رفع شعار جديد
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="logo_file" class="form-label">اختر ملف الشعار:</label>
                                <input type="file" class="form-control" id="logo_file" name="logo_file" 
                                       accept="image/jpeg,image/jpg,image/png,image/gif,.jpg,.jpeg,.png,.gif" required>
                                <div class="form-text">
                                    أنواع الملفات المدعومة: JPG, PNG, GIF<br>
                                    الحد الأقصى: 5 ميجابايت
                                </div>
                            </div>
                            
                            <button type="submit" name="upload_logo" class="btn btn-success w-100">
                                <i class="fas fa-upload me-2"></i>
                                رفع الشعار
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- نصائح -->
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            نصائح
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="mb-0">
                            <li><strong>الحجم المثالي:</strong> 300x200 بكسل</li>
                            <li><strong>الشكل:</strong> مستطيل أفقي</li>
                            <li><strong>الخلفية:</strong> شفافة أو بيضاء</li>
                            <li><strong>الجودة:</strong> عالية ووضوح جيد</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- روابط مفيدة -->
        <div class="text-center text-white mt-4">
            <a href="logo.php" class="btn btn-light btn-lg me-3">
                <i class="fas fa-cogs me-2"></i>
                الرفع المتقدم
            </a>
            <a href="index.php" class="btn btn-outline-light btn-lg me-3">
                <i class="fas fa-cogs me-2"></i>
                الإعدادات
            </a>
            <a href="../../create-test-logo.php" class="btn btn-outline-light btn-lg">
                <i class="fas fa-image me-2"></i>
                إنشاء شعار اختبار
            </a>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
