<?php
/**
 * إدارة المستخدمين والأدوار
 * Users and Roles Management
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";
require_once "../../includes/session-helper.php";

// التحقق من تسجيل الدخول وصلاحية المدير
requireAdmin();

$page_title = "إدارة المستخدمين";
$page_icon = "fas fa-users-cog";

$message = '';
$message_type = '';

// إضافة عمود الحالة إذا لم يكن موجوداً
try {
    $check_column = "SHOW COLUMNS FROM users LIKE 'status'";
    $column_result = $conn->query($check_column);

    if ($column_result->num_rows == 0) {
        $add_column = "ALTER TABLE users ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active'";
        $conn->query($add_column);
    }
} catch (Exception $e) {
    // تجاهل الخطأ إذا كان العمود موجوداً بالفعل
}

// معالجة إضافة مستخدم جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_user'])) {
    try {
        $name = clean($conn, $_POST['name']);
        $username = clean($conn, $_POST['username']);
        $password = $_POST['password'];
        $role = clean($conn, $_POST['role']);
        $email = isset($_POST['email']) ? clean($conn, $_POST['email']) : null;
        $phone = isset($_POST['phone']) ? clean($conn, $_POST['phone']) : null;
        
        // التحقق من عدم تكرار اسم المستخدم
        $check_query = "SELECT id FROM users WHERE username = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("s", $username);
        $check_stmt->execute();
        
        if ($check_stmt->get_result()->num_rows > 0) {
            throw new Exception("اسم المستخدم موجود بالفعل");
        }
        
        // تشفير كلمة المرور
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // إدراج المستخدم الجديد
        $insert_query = "INSERT INTO users (name, username, password, role, email, phone, status) VALUES (?, ?, ?, ?, ?, ?, 'active')";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bind_param("ssssss", $name, $username, $hashed_password, $role, $email, $phone);
        $insert_stmt->execute();
        
        $message = "تم إضافة المستخدم بنجاح";
        $message_type = 'success';
        
    } catch (Exception $e) {
        $message = "خطأ في إضافة المستخدم: " . $e->getMessage();
        $message_type = 'danger';
    }
}

// معالجة تحديث دور المستخدم
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_role'])) {
    try {
        $user_id = intval($_POST['user_id']);
        $new_role = clean($conn, $_POST['new_role']);
        
        // التحقق من صحة الدور
        $valid_roles = ['admin', 'manager', 'cashier'];
        if (!in_array($new_role, $valid_roles)) {
            throw new Exception("دور غير صحيح");
        }
        
        // تحديث دور المستخدم
        $update_query = "UPDATE users SET role = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("si", $new_role, $user_id);
        $update_stmt->execute();
        
        $message = "تم تحديث دور المستخدم بنجاح";
        $message_type = 'success';
        
    } catch (Exception $e) {
        $message = "خطأ في تحديث الدور: " . $e->getMessage();
        $message_type = 'danger';
    }
}

// معالجة تعطيل/تفعيل المستخدم
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['toggle_status'])) {
    try {
        $user_id = intval($_POST['user_id']);
        $new_status = $_POST['new_status'] === 'active' ? 'active' : 'inactive';
        
        // تحديث حالة المستخدم
        $update_query = "UPDATE users SET status = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("si", $new_status, $user_id);
        $update_stmt->execute();
        
        $message = "تم تحديث حالة المستخدم بنجاح";
        $message_type = 'success';
        
    } catch (Exception $e) {
        $message = "خطأ في تحديث الحالة: " . $e->getMessage();
        $message_type = 'danger';
    }
}

// معالجة حذف المستخدم
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_user'])) {
    try {
        $user_id = intval($_POST['user_id']);
        
        // التأكد من عدم حذف المستخدم الحالي
        if ($user_id == $_SESSION['user_id']) {
            throw new Exception("لا يمكن حذف المستخدم الحالي");
        }
        
        // حذف المستخدم
        $delete_query = "DELETE FROM users WHERE id = ?";
        $delete_stmt = $conn->prepare($delete_query);
        $delete_stmt->bind_param("i", $user_id);
        $delete_stmt->execute();
        
        $message = "تم حذف المستخدم بنجاح";
        $message_type = 'success';
        
    } catch (Exception $e) {
        $message = "خطأ في حذف المستخدم: " . $e->getMessage();
        $message_type = 'danger';
    }
}

// الحصول على قائمة المستخدمين
$users_query = "SELECT * FROM users ORDER BY created_at DESC";
$users_result = $conn->query($users_query);

// إحصائيات المستخدمين
$stats_query = "
    SELECT 
        COUNT(*) as total_users,
        SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin_count,
        SUM(CASE WHEN role = 'manager' THEN 1 ELSE 0 END) as manager_count,
        SUM(CASE WHEN role = 'cashier' THEN 1 ELSE 0 END) as cashier_count,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_count
    FROM users
";
$stats_result = $conn->query($stats_query);
$stats = $stats_result->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .role-badge {
            font-size: 0.8em;
        }
        .stats-card {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 15px;
            color: white;
        }
        .stats-card.total {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stats-card.admin {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .stats-card.manager {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        }
        .stats-card.cashier {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0 text-white">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2 text-white-50">إدارة المستخدمين والأدوار والصلاحيات</p>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light btn-lg me-2" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة مستخدم
                    </button>
                    <a href="index.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-cogs me-2"></i>
                        الإعدادات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?php echo ($message_type == 'success') ? 'check-circle' : 'exclamation-circle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            
            <!-- إحصائيات المستخدمين -->
            <div class="col-md-3">
                <div class="stats-card total">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <h3><?php echo $stats['total_users']; ?></h3>
                    <p class="mb-0">إجمالي المستخدمين</p>
                    <small><?php echo $stats['active_count']; ?> نشط</small>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card admin">
                    <i class="fas fa-user-shield fa-2x mb-2"></i>
                    <h3><?php echo $stats['admin_count']; ?></h3>
                    <p class="mb-0">مديرون</p>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card manager">
                    <i class="fas fa-user-tie fa-2x mb-2"></i>
                    <h3><?php echo $stats['manager_count']; ?></h3>
                    <p class="mb-0">مديرو فروع</p>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card cashier">
                    <i class="fas fa-user fa-2x mb-2"></i>
                    <h3><?php echo $stats['cashier_count']; ?></h3>
                    <p class="mb-0">كاشيرون</p>
                </div>
            </div>
        </div>

        <!-- قائمة المستخدمين -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة المستخدمين
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>اسم المستخدم</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($user = $users_result->fetch_assoc()): ?>
                                <tr class="<?php echo ($user['id'] == $_SESSION['user_id']) ? 'table-warning' : ''; ?>">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="user-avatar me-3">
                                                <?php echo strtoupper(substr($user['name'], 0, 2)); ?>
                                            </div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($user['name']); ?></strong>
                                                <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                                    <span class="badge bg-primary ms-1">أنت</span>
                                                <?php endif; ?>
                                                <?php if (!empty($user['email'])): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <code>@<?php echo htmlspecialchars($user['username']); ?></code>
                                        <?php if (!empty($user['phone'])): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($user['phone']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge role-badge bg-<?php echo ($user['role'] == 'admin') ? 'success' : (($user['role'] == 'manager') ? 'warning' : 'secondary'); ?>">
                                            <?php 
                                            $roles = [
                                                'admin' => 'مدير',
                                                'manager' => 'مدير فرع',
                                                'cashier' => 'كاشير'
                                            ];
                                            echo $roles[$user['role']] ?? $user['role'];
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo (($user['status'] ?? 'active') == 'active') ? 'success' : 'danger'; ?>">
                                            <?php echo (($user['status'] ?? 'active') == 'active') ? 'نشط' : 'معطل'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <!-- تغيير الدور -->
                                            <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" 
                                                    data-bs-target="#roleModal" data-user-id="<?php echo $user['id']; ?>" 
                                                    data-user-name="<?php echo htmlspecialchars($user['name']); ?>" 
                                                    data-current-role="<?php echo $user['role']; ?>">
                                                <i class="fas fa-user-cog"></i>
                                            </button>
                                            
                                            <!-- تعطيل/تفعيل -->
                                            <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <input type="hidden" name="new_status" value="<?php echo (($user['status'] ?? 'active') == 'active') ? 'inactive' : 'active'; ?>">
                                                    <button type="submit" name="toggle_status" class="btn btn-sm btn-outline-warning" 
                                                            onclick="return confirm('هل تريد تغيير حالة هذا المستخدم؟')">
                                                        <i class="fas fa-<?php echo (($user['status'] ?? 'active') == 'active') ? 'ban' : 'check'; ?>"></i>
                                                    </button>
                                                </form>
                                                
                                                <!-- حذف -->
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" name="delete_user" class="btn btn-sm btn-outline-danger" 
                                                            onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟\n\nهذا الإجراء لا يمكن التراجع عنه!')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة مستخدم -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">الاسم الكامل:</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">اسم المستخدم:</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">كلمة المرور:</label>
                                <input type="password" class="form-control" id="password" name="password" minlength="6" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">الدور:</label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="cashier">كاشير</option>
                                    <option value="manager">مدير فرع</option>
                                    <option value="admin">مدير</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني:</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف:</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" name="add_user" class="btn btn-primary">إضافة المستخدم</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal تغيير الدور -->
    <div class="modal fade" id="roleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تغيير دور المستخدم</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" id="role_user_id" name="user_id">
                        <p>تغيير دور المستخدم: <strong id="role_user_name"></strong></p>
                        
                        <div class="mb-3">
                            <label for="new_role" class="form-label">الدور الجديد:</label>
                            <select class="form-select" id="new_role" name="new_role" required>
                                <option value="cashier">كاشير</option>
                                <option value="manager">مدير فرع</option>
                                <option value="admin">مدير</option>
                            </select>
                        </div>
                        
                        <div class="alert alert-warning">
                            <strong>تنبيه:</strong> تغيير الدور سيؤثر على صلاحيات المستخدم في النظام
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" name="update_role" class="btn btn-primary">تحديث الدور</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحديث modal تغيير الدور
        document.getElementById('roleModal').addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const userId = button.getAttribute('data-user-id');
            const userName = button.getAttribute('data-user-name');
            const currentRole = button.getAttribute('data-current-role');
            
            document.getElementById('role_user_id').value = userId;
            document.getElementById('role_user_name').textContent = userName;
            document.getElementById('new_role').value = currentRole;
        });
    </script>

</body>
</html>
