<?php
/**
 * صفحة الملف الشخصي للمستخدم
 * User Profile Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "config/db_config.php";
require_once "includes/functions.php";
require_once "includes/session-helper.php";

// التحقق من تسجيل الدخول
requireLogin();

$page_title = "الملف الشخصي";
$page_icon = "fas fa-user-cog";

$message = '';
$message_type = '';

// الحصول على بيانات المستخدم الحالي
$user_id = $_SESSION["user_id"];
$user_query = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($user_query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$user_result = $stmt->get_result();
$user_data = $user_result->fetch_assoc();

// معالجة تحديث البيانات الشخصية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    try {
        $name = clean($conn, $_POST['name']);
        $username = clean($conn, $_POST['username']);
        $email = clean($conn, $_POST['email']);
        $phone = clean($conn, $_POST['phone']);
        
        // التحقق من عدم تكرار اسم المستخدم
        $check_username = "SELECT id FROM users WHERE username = ? AND id != ?";
        $check_stmt = $conn->prepare($check_username);
        $check_stmt->bind_param("si", $username, $user_id);
        $check_stmt->execute();
        
        if ($check_stmt->get_result()->num_rows > 0) {
            throw new Exception("اسم المستخدم موجود بالفعل");
        }
        
        // تحديث البيانات
        $update_query = "UPDATE users SET name = ?, username = ?, email = ?, phone = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("ssssi", $name, $username, $email, $phone, $user_id);
        $update_stmt->execute();
        
        // تحديث الجلسة
        $_SESSION['user_name'] = $name;
        $_SESSION['username'] = $username;
        
        // إعادة تحميل بيانات المستخدم
        $stmt->execute();
        $user_data = $stmt->get_result()->fetch_assoc();
        
        $message = "تم تحديث البيانات الشخصية بنجاح";
        $message_type = 'success';
        
    } catch (Exception $e) {
        $message = "خطأ في تحديث البيانات: " . $e->getMessage();
        $message_type = 'danger';
    }
}

// معالجة تغيير كلمة المرور
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    try {
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        // التحقق من كلمة المرور الحالية
        if (!password_verify($current_password, $user_data['password'])) {
            throw new Exception("كلمة المرور الحالية غير صحيحة");
        }
        
        // التحقق من تطابق كلمة المرور الجديدة
        if ($new_password !== $confirm_password) {
            throw new Exception("كلمة المرور الجديدة غير متطابقة");
        }
        
        // التحقق من قوة كلمة المرور
        if (strlen($new_password) < 6) {
            throw new Exception("كلمة المرور يجب أن تكون 6 أحرف على الأقل");
        }
        
        // تشفير كلمة المرور الجديدة
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        
        // تحديث كلمة المرور
        $update_password_query = "UPDATE users SET password = ? WHERE id = ?";
        $password_stmt = $conn->prepare($update_password_query);
        $password_stmt->bind_param("si", $hashed_password, $user_id);
        $password_stmt->execute();
        
        $message = "تم تغيير كلمة المرور بنجاح";
        $message_type = 'success';
        
    } catch (Exception $e) {
        $message = "خطأ في تغيير كلمة المرور: " . $e->getMessage();
        $message_type = 'danger';
    }
}

// الحصول على إحصائيات المستخدم
$stats_query = "
    SELECT 
        (SELECT COUNT(*) FROM sales WHERE user_id = ?) as total_sales,
        (SELECT COUNT(*) FROM purchases WHERE user_id = ?) as total_purchases,
        (SELECT COUNT(*) FROM expenses WHERE user_id = ?) as total_expenses,
        (SELECT SUM(final_amount) FROM sales WHERE user_id = ?) as total_sales_amount
";
$stats_stmt = $conn->prepare($stats_query);
$stats_stmt->bind_param("iiii", $user_id, $user_id, $user_id, $user_id);
$stats_stmt->execute();
$stats = $stats_stmt->get_result()->fetch_assoc();

// الحصول على آخر الأنشطة
$activities_query = "
    (SELECT 'sale' as type, id, final_amount as amount, created_at FROM sales WHERE user_id = ? ORDER BY created_at DESC LIMIT 5)
    UNION ALL
    (SELECT 'purchase' as type, id, total_amount as amount, created_at FROM purchases WHERE user_id = ? ORDER BY created_at DESC LIMIT 5)
    UNION ALL
    (SELECT 'expense' as type, id, amount, created_at FROM expenses WHERE user_id = ? ORDER BY created_at DESC LIMIT 5)
    ORDER BY created_at DESC LIMIT 10
";
$activities_stmt = $conn->prepare($activities_query);
$activities_stmt->bind_param("iii", $user_id, $user_id, $user_id);
$activities_stmt->execute();
$activities = $activities_stmt->get_result();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            margin: 0 auto 20px;
        }
        .stat-card {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 15px;
        }
        .stat-card.sales {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        .stat-card.purchases {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
        }
        .stat-card.expenses {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }
        .activity-item {
            padding: 10px;
            border-left: 4px solid #007bff;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .activity-item.sale {
            border-left-color: #28a745;
        }
        .activity-item.purchase {
            border-left-color: #007bff;
        }
        .activity-item.expense {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0 text-white">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2 text-white-50">إدارة بياناتك الشخصية وإعدادات الحساب</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="index.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                <i class="fas fa-<?php echo ($message_type == 'success') ? 'check-circle' : 'exclamation-circle'; ?> me-2"></i>
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            
            <!-- معلومات المستخدم والإحصائيات -->
            <div class="col-md-4">
                
                <!-- بطاقة المستخدم -->
                <div class="card">
                    <div class="card-body text-center">
                        <div class="profile-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <h4><?php echo htmlspecialchars($user_data['name']); ?></h4>
                        <p class="text-muted">@<?php echo htmlspecialchars($user_data['username']); ?></p>
                        <span class="badge bg-<?php echo ($user_data['role'] == 'admin') ? 'success' : (($user_data['role'] == 'manager') ? 'warning' : 'secondary'); ?> fs-6">
                            <?php 
                            $roles = [
                                'admin' => 'مدير',
                                'manager' => 'مدير فرع',
                                'cashier' => 'كاشير'
                            ];
                            echo $roles[$user_data['role']] ?? $user_data['role'];
                            ?>
                        </span>
                        
                        <hr>
                        
                        <div class="row text-center">
                            <div class="col-6">
                                <small class="text-muted">تاريخ الانضمام</small>
                                <div><?php echo date('Y-m-d', strtotime($user_data['created_at'])); ?></div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">آخر تسجيل دخول</small>
                                <div><?php echo date('H:i'); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- إحصائيات المستخدم -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            إحصائياتي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="stat-card sales">
                            <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                            <h3><?php echo number_format($stats['total_sales'] ?? 0); ?></h3>
                            <p class="mb-0">إجمالي المبيعات</p>
                            <small><?php echo formatMoney($stats['total_sales_amount'] ?? 0); ?></small>
                        </div>
                        
                        <div class="stat-card purchases">
                            <i class="fas fa-truck fa-2x mb-2"></i>
                            <h3><?php echo number_format($stats['total_purchases'] ?? 0); ?></h3>
                            <p class="mb-0">إجمالي المشتريات</p>
                        </div>
                        
                        <div class="stat-card expenses">
                            <i class="fas fa-money-bill-alt fa-2x mb-2"></i>
                            <h3><?php echo number_format($stats['total_expenses'] ?? 0); ?></h3>
                            <p class="mb-0">إجمالي المصروفات</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- تعديل البيانات -->
            <div class="col-md-8">
                
                <!-- تحديث البيانات الشخصية -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            تحديث البيانات الشخصية
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">الاسم الكامل:</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="<?php echo htmlspecialchars($user_data['name']); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">اسم المستخدم:</label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?php echo htmlspecialchars($user_data['username']); ?>" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني:</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($user_data['email'] ?? ''); ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف:</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?php echo htmlspecialchars($user_data['phone'] ?? ''); ?>">
                                </div>
                            </div>
                            
                            <div class="text-end">
                                <button type="submit" name="update_profile" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- تغيير كلمة المرور -->
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-key me-2"></i>
                            تغيير كلمة المرور
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label for="current_password" class="form-label">كلمة المرور الحالية:</label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="new_password" class="form-label">كلمة المرور الجديدة:</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password" 
                                           minlength="6" required>
                                    <div class="form-text">يجب أن تكون 6 أحرف على الأقل</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور:</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                           minlength="6" required>
                                </div>
                            </div>
                            
                            <div class="text-end">
                                <button type="submit" name="change_password" class="btn btn-warning">
                                    <i class="fas fa-key me-2"></i>
                                    تغيير كلمة المرور
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- آخر الأنشطة -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            آخر الأنشطة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($activities->num_rows > 0): ?>
                            <?php while ($activity = $activities->fetch_assoc()): ?>
                                <div class="activity-item <?php echo $activity['type']; ?>">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>
                                                <?php 
                                                $activity_names = [
                                                    'sale' => 'مبيعة',
                                                    'purchase' => 'مشتريات',
                                                    'expense' => 'مصروف'
                                                ];
                                                echo $activity_names[$activity['type']];
                                                ?>
                                                #<?php echo $activity['id']; ?>
                                            </strong>
                                            <div class="small text-muted">
                                                <?php echo date('Y-m-d H:i', strtotime($activity['created_at'])); ?>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-<?php echo ($activity['type'] == 'sale') ? 'success' : (($activity['type'] == 'purchase') ? 'primary' : 'danger'); ?>">
                                                <?php echo formatMoney($activity['amount']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <div class="text-center py-3">
                                <i class="fas fa-history fa-2x text-muted mb-2"></i>
                                <p class="text-muted">لا توجد أنشطة حتى الآن</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // التحقق من تطابق كلمة المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>

</body>
</html>
