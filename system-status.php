<?php
/**
 * حالة النظام النهائية
 * Final System Status
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة النظام - نظام Zero</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        .status-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 8px;
        }
        .status-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status-danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                
                <!-- العنوان الرئيسي -->
                <div class="text-center mb-4">
                    <h1 class="text-white mb-3">
                        <i class="fas fa-chart-line me-3"></i>
                        حالة النظام النهائية
                    </h1>
                    <p class="text-white-50">تقرير شامل عن حالة نظام Zero</p>
                </div>

                <!-- فحص قاعدة البيانات -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3><i class="fas fa-database me-2"></i>فحص قاعدة البيانات</h3>
                    </div>
                    <div class="card-body">
                        
                        <?php
                        try {
                            // الاتصال بقاعدة البيانات
                            $conn = new mysqli('localhost', 'root', '', 'zero');
                            $conn->set_charset("utf8mb4");
                            
                            if ($conn->connect_error) {
                                throw new Exception("فشل الاتصال: " . $conn->connect_error);
                            }
                            
                            echo "<div class='status-item status-success'>";
                            echo "<i class='fas fa-check-circle me-2'></i>";
                            echo "<strong>الاتصال بقاعدة البيانات:</strong> ناجح";
                            echo "</div>";
                            
                            // فحص الجداول المطلوبة
                            $required_tables = [
                                'users' => 'المستخدمين',
                                'treasury' => 'الخزينة',
                                'sales' => 'المبيعات',
                                'purchases' => 'المشتريات',
                                'products' => 'المنتجات',
                                'customers' => 'العملاء',
                                'suppliers' => 'الموردين',
                                'categories' => 'الفئات',
                                'expenses' => 'المصروفات',
                                'settings' => 'الإعدادات'
                            ];
                            
                            $missing_tables = [];
                            $existing_tables = [];
                            
                            foreach ($required_tables as $table => $name) {
                                $result = $conn->query("SHOW TABLES LIKE '$table'");
                                if ($result->num_rows > 0) {
                                    $existing_tables[] = $name;
                                    echo "<div class='status-item status-success'>";
                                    echo "<i class='fas fa-table me-2'></i>";
                                    echo "<strong>جدول $name ($table):</strong> موجود";
                                    echo "</div>";
                                } else {
                                    $missing_tables[] = $name;
                                    echo "<div class='status-item status-danger'>";
                                    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                                    echo "<strong>جدول $name ($table):</strong> مفقود";
                                    echo "</div>";
                                }
                            }
                            
                            // فحص البيانات الأساسية
                            echo "<h5 class='mt-4'>البيانات الأساسية:</h5>";
                            
                            // فحص المستخدمين
                            $users_count = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
                            if ($users_count > 0) {
                                echo "<div class='status-item status-success'>";
                                echo "<i class='fas fa-users me-2'></i>";
                                echo "<strong>المستخدمين:</strong> $users_count مستخدم";
                                echo "</div>";
                            } else {
                                echo "<div class='status-item status-warning'>";
                                echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                                echo "<strong>المستخدمين:</strong> لا يوجد مستخدمين";
                                echo "</div>";
                            }
                            
                            // فحص العملاء
                            $customers_count = $conn->query("SELECT COUNT(*) as count FROM customers")->fetch_assoc()['count'];
                            echo "<div class='status-item status-success'>";
                            echo "<i class='fas fa-user-friends me-2'></i>";
                            echo "<strong>العملاء:</strong> $customers_count عميل";
                            echo "</div>";
                            
                            // فحص المنتجات
                            $products_count = $conn->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
                            echo "<div class='status-item status-success'>";
                            echo "<i class='fas fa-box me-2'></i>";
                            echo "<strong>المنتجات:</strong> $products_count منتج";
                            echo "</div>";
                            
                            // فحص الخزينة
                            $treasury_result = $conn->query("SELECT balance_after FROM treasury ORDER BY id DESC LIMIT 1");
                            if ($treasury_result->num_rows > 0) {
                                $balance = $treasury_result->fetch_assoc()['balance_after'];
                                echo "<div class='status-item status-success'>";
                                echo "<i class='fas fa-money-bill-wave me-2'></i>";
                                echo "<strong>رصيد الخزينة:</strong> " . number_format($balance, 2) . " ريال";
                                echo "</div>";
                            } else {
                                echo "<div class='status-item status-warning'>";
                                echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                                echo "<strong>الخزينة:</strong> لا توجد معاملات";
                                echo "</div>";
                            }
                            
                            $conn->close();
                            
                        } catch (Exception $e) {
                            echo "<div class='status-item status-danger'>";
                            echo "<i class='fas fa-times-circle me-2'></i>";
                            echo "<strong>خطأ:</strong> " . $e->getMessage();
                            echo "</div>";
                        }
                        ?>
                        
                    </div>
                </div>

                <!-- فحص الملفات -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h3><i class="fas fa-file-code me-2"></i>فحص الملفات الأساسية</h3>
                    </div>
                    <div class="card-body">
                        
                        <?php
                        $core_files = [
                            'index.php' => 'الصفحة الرئيسية',
                            'login.php' => 'تسجيل الدخول',
                            'config/db_config.php' => 'إعدادات قاعدة البيانات',
                            'includes/functions.php' => 'الدوال المساعدة',
                            'includes/header.php' => 'رأس الصفحة',
                            'includes/footer.php' => 'تذييل الصفحة'
                        ];
                        
                        foreach ($core_files as $file => $name) {
                            if (file_exists($file)) {
                                echo "<div class='status-item status-success'>";
                                echo "<i class='fas fa-file-check me-2'></i>";
                                echo "<strong>$name:</strong> موجود";
                                echo "</div>";
                            } else {
                                echo "<div class='status-item status-danger'>";
                                echo "<i class='fas fa-file-times me-2'></i>";
                                echo "<strong>$name:</strong> مفقود";
                                echo "</div>";
                            }
                        }
                        ?>
                        
                    </div>
                </div>

                <!-- الإحصائيات -->
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h3><i class="fas fa-chart-bar me-2"></i>الإحصائيات</h3>
                    </div>
                    <div class="card-body">
                        
                        <?php
                        try {
                            $conn = new mysqli('localhost', 'root', '', 'zero');
                            $conn->set_charset("utf8mb4");
                            
                            // إحصائيات اليوم
                            $today = date('Y-m-d');
                            
                            // مبيعات اليوم
                            $today_sales = $conn->query("SELECT COUNT(*) as count, COALESCE(SUM(final_amount), 0) as total FROM sales WHERE sale_date = '$today'")->fetch_assoc();
                            
                            echo "<div class='row'>";
                            echo "<div class='col-md-3'>";
                            echo "<div class='status-item status-success text-center'>";
                            echo "<h4>" . $today_sales['count'] . "</h4>";
                            echo "<p>مبيعات اليوم</p>";
                            echo "</div>";
                            echo "</div>";
                            
                            echo "<div class='col-md-3'>";
                            echo "<div class='status-item status-success text-center'>";
                            echo "<h4>" . number_format($today_sales['total'], 2) . "</h4>";
                            echo "<p>إجمالي مبيعات اليوم</p>";
                            echo "</div>";
                            echo "</div>";
                            
                            // مشتريات اليوم
                            $today_purchases = $conn->query("SELECT COUNT(*) as count, COALESCE(SUM(final_amount), 0) as total FROM purchases WHERE purchase_date = '$today'")->fetch_assoc();
                            
                            echo "<div class='col-md-3'>";
                            echo "<div class='status-item status-success text-center'>";
                            echo "<h4>" . $today_purchases['count'] . "</h4>";
                            echo "<p>مشتريات اليوم</p>";
                            echo "</div>";
                            echo "</div>";
                            
                            echo "<div class='col-md-3'>";
                            echo "<div class='status-item status-success text-center'>";
                            echo "<h4>" . number_format($today_purchases['total'], 2) . "</h4>";
                            echo "<p>إجمالي مشتريات اليوم</p>";
                            echo "</div>";
                            echo "</div>";
                            echo "</div>";
                            
                            $conn->close();
                            
                        } catch (Exception $e) {
                            echo "<div class='status-item status-danger'>";
                            echo "<i class='fas fa-times-circle me-2'></i>";
                            echo "<strong>خطأ في الإحصائيات:</strong> " . $e->getMessage();
                            echo "</div>";
                        }
                        ?>
                        
                    </div>
                </div>

                <!-- الروابط السريعة -->
                <div class="card">
                    <div class="card-header bg-dark text-white">
                        <h3><i class="fas fa-link me-2"></i>الروابط السريعة</h3>
                    </div>
                    <div class="card-body">
                        
                        <div class="row">
                            <div class="col-md-3">
                                <a href="index.php" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="login.php" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="http://localhost/phpmyadmin" target="_blank" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-database me-2"></i>phpMyAdmin
                                </a>
                            </div>
                            <div class="col-md-3">
                                <button onclick="location.reload()" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-sync-alt me-2"></i>تحديث التقرير
                                </button>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-key me-2"></i>بيانات تسجيل الدخول:</h6>
                            <p class="mb-0">
                                <strong>اسم المستخدم:</strong> <code>admin</code><br>
                                <strong>كلمة المرور:</strong> <code>admin</code>
                            </p>
                        </div>
                        
                        <div class="alert alert-success mt-3">
                            <h6><i class='fas fa-check-circle me-2'></i>حالة النظام العامة:</h6>
                            <p class="mb-0"><strong>نظام Zero جاهز للاستخدام بشكل مثالي!</strong></p>
                        </div>
                        
                        <div class="alert alert-warning mt-3">
                            <strong>تنبيه أمني:</strong> احذف هذا الملف بعد التأكد من حالة النظام.
                        </div>
                        
                    </div>
                </div>
                
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
