<?php
/**
 * اختبار نظام الرصيد المتقدم
 * Test Advanced Balance System
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "config/db_config.php";
require_once "includes/functions.php";

// التحقق من وجود ملف الوظائف المتقدمة
$advanced_functions_file = "includes/advanced-balance-functions.php";
$advanced_functions_exists = file_exists($advanced_functions_file);

if ($advanced_functions_exists) {
    require_once $advanced_functions_file;
}

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

$page_title = "اختبار نظام الرصيد المتقدم";
$test_results = [];
$errors = [];

// اختبار الاتصال بقاعدة البيانات
try {
    $test_results['database_connection'] = $conn ? true : false;
} catch (Exception $e) {
    $test_results['database_connection'] = false;
    $errors[] = "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
}

// اختبار وجود الجداول والأعمدة المطلوبة
$required_columns = [
    'customers' => ['wallet_balance', 'debt_balance', 'credit_limit', 'last_transaction_date'],
    'suppliers' => ['wallet_balance', 'debt_balance', 'credit_limit', 'last_transaction_date'],
    'sales' => ['wallet_used', 'cash_paid', 'change_amount', 'wallet_added', 'debt_amount']
];

$required_tables = ['balance_transactions', 'balance_settings'];

// فحص الأعمدة
foreach ($required_columns as $table => $columns) {
    foreach ($columns as $column) {
        try {
            $query = "SHOW COLUMNS FROM `$table` LIKE '$column'";
            $result = $conn->query($query);
            $test_results["column_{$table}_{$column}"] = $result && $result->num_rows > 0;
        } catch (Exception $e) {
            $test_results["column_{$table}_{$column}"] = false;
            $errors[] = "خطأ في فحص العمود $column في جدول $table: " . $e->getMessage();
        }
    }
}

// فحص الجداول
foreach ($required_tables as $table) {
    try {
        $query = "SHOW TABLES LIKE '$table'";
        $result = $conn->query($query);
        $test_results["table_$table"] = $result && $result->num_rows > 0;
    } catch (Exception $e) {
        $test_results["table_$table"] = false;
        $errors[] = "خطأ في فحص الجدول $table: " . $e->getMessage();
    }
}

// اختبار الوظائف المطلوبة
$required_functions = [
    'generateSaleNumber',
    'formatMoney',
    'clean'
];

if ($advanced_functions_exists) {
    $advanced_functions = [
        'getBalanceSetting',
        'getEntityBalances',
        'addWalletBalance',
        'useWalletBalance',
        'processAdvancedPayment'
    ];
    $required_functions = array_merge($required_functions, $advanced_functions);
}

foreach ($required_functions as $function) {
    $test_results["function_$function"] = function_exists($function);
}

// اختبار بيانات العملاء
try {
    $customers_query = "SELECT COUNT(*) as count FROM customers WHERE id > 1";
    $customers_result = $conn->query($customers_query);
    $customers_count = $customers_result->fetch_assoc()['count'];
    $test_results['customers_data'] = $customers_count > 0;
    $test_results['customers_count'] = $customers_count;
} catch (Exception $e) {
    $test_results['customers_data'] = false;
    $errors[] = "خطأ في فحص بيانات العملاء: " . $e->getMessage();
}

// اختبار بيانات المنتجات
try {
    $products_query = "SELECT COUNT(*) as count FROM products WHERE stock_quantity > 0";
    $products_result = $conn->query($products_query);
    $products_count = $products_result->fetch_assoc()['count'];
    $test_results['products_data'] = $products_count > 0;
    $test_results['products_count'] = $products_count;
} catch (Exception $e) {
    $test_results['products_data'] = false;
    $errors[] = "خطأ في فحص بيانات المنتجات: " . $e->getMessage();
}

// حساب النتيجة الإجمالية
$total_tests = count($test_results);
$passed_tests = array_sum(array_map(function($result) {
    return is_bool($result) ? ($result ? 1 : 0) : 0;
}, $test_results));

$success_percentage = $total_tests > 0 ? ($passed_tests / $total_tests) * 100 : 0;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .test-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px 15px;
            margin-bottom: 8px;
            border-left: 4px solid #007bff;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-pass {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .test-fail {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .progress-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: conic-gradient(#28a745 0deg <?php echo $success_percentage * 3.6; ?>deg, #e9ecef <?php echo $success_percentage * 3.6; ?>deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }
        .progress-inner {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-vial me-3"></i>
                اختبار نظام الرصيد المتقدم
            </h1>
            <p class="lead">فحص شامل لجميع مكونات النظام</p>
            
            <!-- مؤشر النجاح -->
            <div class="progress-circle mt-4">
                <div class="progress-inner">
                    <div style="font-size: 1.5rem;"><?php echo round($success_percentage); ?>%</div>
                    <div style="font-size: 0.9rem;">نجح</div>
                </div>
            </div>
            <p class="mt-2"><?php echo $passed_tests; ?> من <?php echo $total_tests; ?> اختبار نجح</p>
        </div>

        <!-- رسائل الخطأ -->
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle me-2"></i>أخطاء الاختبار:</h5>
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- نتائج الاختبار -->
        <div class="row">
            
            <!-- اختبار قاعدة البيانات -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-database me-2"></i>اختبار قاعدة البيانات</h5>
                    </div>
                    <div class="card-body">
                        
                        <div class="test-item <?php echo $test_results['database_connection'] ? 'test-pass' : 'test-fail'; ?>">
                            <span>الاتصال بقاعدة البيانات</span>
                            <i class="fas fa-<?php echo $test_results['database_connection'] ? 'check text-success' : 'times text-danger'; ?>"></i>
                        </div>
                        
                        <h6 class="mt-3">جدول العملاء:</h6>
                        <?php foreach ($required_columns['customers'] as $column): ?>
                            <div class="test-item <?php echo $test_results["column_customers_$column"] ? 'test-pass' : 'test-fail'; ?>">
                                <span><?php echo $column; ?></span>
                                <i class="fas fa-<?php echo $test_results["column_customers_$column"] ? 'check text-success' : 'times text-danger'; ?>"></i>
                            </div>
                        <?php endforeach; ?>
                        
                        <h6 class="mt-3">جدول المبيعات:</h6>
                        <?php foreach ($required_columns['sales'] as $column): ?>
                            <div class="test-item <?php echo $test_results["column_sales_$column"] ? 'test-pass' : 'test-fail'; ?>">
                                <span><?php echo $column; ?></span>
                                <i class="fas fa-<?php echo $test_results["column_sales_$column"] ? 'check text-success' : 'times text-danger'; ?>"></i>
                            </div>
                        <?php endforeach; ?>
                        
                        <h6 class="mt-3">الجداول الجديدة:</h6>
                        <?php foreach ($required_tables as $table): ?>
                            <div class="test-item <?php echo $test_results["table_$table"] ? 'test-pass' : 'test-fail'; ?>">
                                <span><?php echo $table; ?></span>
                                <i class="fas fa-<?php echo $test_results["table_$table"] ? 'check text-success' : 'times text-danger'; ?>"></i>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- اختبار الوظائف والبيانات -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-code me-2"></i>اختبار الوظائف والبيانات</h5>
                    </div>
                    <div class="card-body">
                        
                        <h6>الوظائف الأساسية:</h6>
                        <?php foreach (['generateSaleNumber', 'formatMoney', 'clean'] as $function): ?>
                            <div class="test-item <?php echo $test_results["function_$function"] ? 'test-pass' : 'test-fail'; ?>">
                                <span><?php echo $function; ?>()</span>
                                <i class="fas fa-<?php echo $test_results["function_$function"] ? 'check text-success' : 'times text-danger'; ?>"></i>
                            </div>
                        <?php endforeach; ?>
                        
                        <?php if ($advanced_functions_exists): ?>
                            <h6 class="mt-3">الوظائف المتقدمة:</h6>
                            <?php foreach (['getBalanceSetting', 'getEntityBalances', 'addWalletBalance', 'useWalletBalance', 'processAdvancedPayment'] as $function): ?>
                                <div class="test-item <?php echo $test_results["function_$function"] ? 'test-pass' : 'test-fail'; ?>">
                                    <span><?php echo $function; ?>()</span>
                                    <i class="fas fa-<?php echo $test_results["function_$function"] ? 'check text-success' : 'times text-danger'; ?>"></i>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <small>ملف الوظائف المتقدمة غير موجود: <?php echo $advanced_functions_file; ?></small>
                            </div>
                        <?php endif; ?>
                        
                        <h6 class="mt-3">البيانات:</h6>
                        <div class="test-item <?php echo $test_results['customers_data'] ? 'test-pass' : 'test-fail'; ?>">
                            <span>العملاء (<?php echo $test_results['customers_count'] ?? 0; ?>)</span>
                            <i class="fas fa-<?php echo $test_results['customers_data'] ? 'check text-success' : 'times text-danger'; ?>"></i>
                        </div>
                        
                        <div class="test-item <?php echo $test_results['products_data'] ? 'test-pass' : 'test-fail'; ?>">
                            <span>المنتجات (<?php echo $test_results['products_count'] ?? 0; ?>)</span>
                            <i class="fas fa-<?php echo $test_results['products_data'] ? 'check text-success' : 'times text-danger'; ?>"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإجراءات -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-tools me-2"></i>الإجراءات المقترحة</h5>
            </div>
            <div class="card-body">
                
                <?php if ($success_percentage >= 90): ?>
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>النظام جاهز للاستخدام!</h6>
                        <p class="mb-0">جميع المكونات الأساسية تعمل بشكل صحيح. يمكنك البدء في استخدام نظام الرصيد المتقدم.</p>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="pages/sales/add-with-advanced-balance.php" class="btn btn-primary btn-lg me-md-2">
                            <i class="fas fa-cash-register me-2"></i>
                            تجربة نظام المبيعات المتقدم
                        </a>
                        <a href="pages/customers/advanced-balance.php" class="btn btn-success btn-lg me-md-2">
                            <i class="fas fa-balance-scale me-2"></i>
                            إدارة أرصدة العملاء
                        </a>
                        <a href="advanced-balance-system-guide.php" class="btn btn-info btn-lg">
                            <i class="fas fa-book me-2"></i>
                            دليل النظام
                        </a>
                    </div>
                    
                <?php elseif ($success_percentage >= 70): ?>
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>النظام يحتاج بعض التحسينات</h6>
                        <p class="mb-0">معظم المكونات تعمل، لكن هناك بعض المشاكل التي تحتاج إصلاح.</p>
                    </div>
                    
                    <a href="apply-balance-update-final.php" class="btn btn-warning btn-lg">
                        <i class="fas fa-wrench me-2"></i>
                        تطبيق التحديثات المطلوبة
                    </a>
                    
                <?php else: ?>
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-times-circle me-2"></i>النظام يحتاج تحديثات أساسية</h6>
                        <p class="mb-0">هناك مشاكل أساسية تحتاج إصلاح قبل استخدام النظام.</p>
                    </div>
                    
                    <a href="apply-balance-update-final.php" class="btn btn-danger btn-lg">
                        <i class="fas fa-database me-2"></i>
                        تطبيق التحديثات الأساسية
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- الخاتمة -->
        <div class="text-center text-white mt-4">
            <h2>🔬 اختبار شامل مكتمل! 🔬</h2>
            <p class="lead">تم فحص جميع مكونات نظام الرصيد المتقدم</p>
            
            <a href="index.php" class="btn btn-light btn-lg mt-3">
                <i class="fas fa-home me-2"></i>
                العودة للصفحة الرئيسية
            </a>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
