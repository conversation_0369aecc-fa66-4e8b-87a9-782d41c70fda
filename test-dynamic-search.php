<?php
/**
 * اختبار البحث الديناميكي في الفاتورة المتطورة
 * Test Dynamic Search in Advanced Invoice
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "config/db_config.php";
require_once "includes/functions.php";

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// الحصول على المنتجات للاختبار
$products_query = "SELECT p.*, c.name as category_name 
                   FROM products p 
                   LEFT JOIN categories c ON p.category_id = c.id 
                   WHERE p.stock_quantity > 0 
                   ORDER BY p.name LIMIT 10";
$products_result = $conn->query($products_query);
$products = [];
while ($product = $products_result->fetch_assoc()) {
    $products[] = $product;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البحث الديناميكي - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .search-container {
            position: relative;
        }
        .search-results {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-top: none;
            background: white;
            position: absolute;
            width: 100%;
            z-index: 1000;
            display: none;
            border-radius: 0 0 5px 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .search-result-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }
        .search-result-item:hover {
            background-color: #f8f9fa;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .test-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-search me-3"></i>
                اختبار البحث الديناميكي
            </h1>
            <p class="lead">تشخيص وإصلاح مشاكل البحث في الفاتورة المتطورة</p>
        </div>

        <!-- نتائج الاختبار -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-vial me-2"></i>نتائج الاختبار</h3>
            </div>
            <div class="card-body">
                <div id="testResults">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>

        <!-- اختبار Select2 -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-search me-2"></i>اختبار Select2</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8 mb-3">
                        <label for="select2Test" class="form-label">البحث باستخدام Select2:</label>
                        <div class="search-container">
                            <select class="form-select" id="select2Test" style="width: 100%;">
                                <option value="">ابحث عن منتج...</option>
                                <?php foreach ($products as $product): ?>
                                    <option value="<?php echo $product['id']; ?>">
                                        <?php echo htmlspecialchars($product['name']); ?>
                                        <?php if ($product['code']): ?>
                                            - كود: <?php echo htmlspecialchars($product['code']); ?>
                                        <?php endif; ?>
                                        - سعر: <?php echo $product['selling_price']; ?> ريال
                                        - مخزون: <?php echo $product['stock_quantity']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-primary w-100 d-block" onclick="testSelect2()">
                            <i class="fas fa-test-tube me-2"></i>اختبار Select2
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- اختبار البحث البديل -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h3><i class="fas fa-keyboard me-2"></i>اختبار البحث البديل</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8 mb-3">
                        <label for="fallbackTest" class="form-label">البحث البديل:</label>
                        <div class="search-container">
                            <input type="text" class="form-control" id="fallbackTest" 
                                   placeholder="ابحث عن منتج بالاسم أو الكود...">
                            <div class="search-results" id="fallbackResults"></div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-success w-100 d-block" onclick="testFallbackSearch()">
                            <i class="fas fa-search me-2"></i>اختبار البحث البديل
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات المنتجات -->
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-box me-2"></i>المنتجات المتاحة للاختبار</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>المعرف</th>
                                <th>الاسم</th>
                                <th>الكود</th>
                                <th>الفئة</th>
                                <th>السعر</th>
                                <th>المخزون</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($products as $product): ?>
                                <tr>
                                    <td><?php echo $product['id']; ?></td>
                                    <td><?php echo htmlspecialchars($product['name']); ?></td>
                                    <td><?php echo htmlspecialchars($product['code'] ?? 'غير محدد'); ?></td>
                                    <td><?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?></td>
                                    <td><?php echo $product['selling_price']; ?> ريال</td>
                                    <td><?php echo $product['stock_quantity']; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- روابط مفيدة -->
        <div class="text-center text-white mt-4">
            <div class="row">
                <div class="col-md-3">
                    <a href="pages/sales/add-advanced.php" class="btn btn-light btn-lg w-100 mb-2">
                        <i class="fas fa-barcode me-2"></i>الفاتورة المتطورة
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="pages/sales/add.php" class="btn btn-primary btn-lg w-100 mb-2">
                        <i class="fas fa-plus me-2"></i>الفاتورة العادية
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="pages/products/index.php" class="btn btn-success btn-lg w-100 mb-2">
                        <i class="fas fa-box me-2"></i>المنتجات
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="index.php" class="btn btn-secondary btn-lg w-100 mb-2">
                        <i class="fas fa-home me-2"></i>الرئيسية
                    </a>
                </div>
            </div>
            
            <div class="alert alert-warning mt-4">
                <strong>ملاحظة:</strong> احذف هذا الملف بعد إصلاح مشاكل البحث.
            </div>
        </div>

    </div>

    <!-- JavaScript Files -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <script>
        // بيانات المنتجات
        const products = <?php echo json_encode($products); ?>;
        
        // وظيفة إضافة نتيجة اختبار
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            resultDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle me-2"></i>${message}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        // اختبار تحميل المكتبات
        document.addEventListener('DOMContentLoaded', function() {
            addTestResult('بدء اختبار النظام...', 'info');
            
            // اختبار jQuery
            if (typeof $ !== 'undefined') {
                addTestResult('jQuery محمل بنجاح - الإصدار: ' + $.fn.jquery, 'success');
            } else {
                addTestResult('jQuery غير محمل!', 'error');
                return;
            }
            
            // اختبار Select2
            if (typeof $.fn.select2 !== 'undefined') {
                addTestResult('Select2 محمل بنجاح', 'success');
                
                // تهيئة Select2
                try {
                    $('#select2Test').select2({
                        theme: 'bootstrap-5',
                        placeholder: 'ابحث عن منتج...',
                        allowClear: true,
                        width: '100%',
                        language: {
                            noResults: function() {
                                return "لا توجد نتائج";
                            },
                            searching: function() {
                                return "جاري البحث...";
                            }
                        }
                    });
                    addTestResult('تم تهيئة Select2 بنجاح', 'success');
                } catch (error) {
                    addTestResult('خطأ في تهيئة Select2: ' + error.message, 'error');
                }
            } else {
                addTestResult('Select2 غير محمل!', 'error');
            }
            
            // إعداد البحث البديل
            setupFallbackSearch();
            
            addTestResult(`تم تحميل ${products.length} منتج للاختبار`, 'info');
        });
        
        // اختبار Select2
        function testSelect2() {
            const selectedValue = $('#select2Test').val();
            if (selectedValue) {
                const selectedProduct = products.find(p => p.id == selectedValue);
                if (selectedProduct) {
                    addTestResult(`تم اختيار المنتج: ${selectedProduct.name}`, 'success');
                } else {
                    addTestResult('خطأ: لم يتم العثور على المنتج المحدد', 'error');
                }
            } else {
                addTestResult('يرجى اختيار منتج أولاً', 'error');
            }
        }
        
        // إعداد البحث البديل
        function setupFallbackSearch() {
            const searchInput = document.getElementById('fallbackTest');
            const resultsDiv = document.getElementById('fallbackResults');
            
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                resultsDiv.innerHTML = '';
                
                if (searchTerm.length < 2) {
                    resultsDiv.style.display = 'none';
                    return;
                }
                
                const filteredProducts = products.filter(product => 
                    product.name.toLowerCase().includes(searchTerm) ||
                    (product.code && product.code.toLowerCase().includes(searchTerm))
                );
                
                if (filteredProducts.length > 0) {
                    filteredProducts.slice(0, 5).forEach(product => {
                        const item = document.createElement('div');
                        item.className = 'search-result-item';
                        item.innerHTML = `
                            <strong>${product.name}</strong><br>
                            <small>كود: ${product.code || 'غير محدد'} - سعر: ${product.selling_price} ريال - مخزون: ${product.stock_quantity}</small>
                        `;
                        
                        item.addEventListener('click', function() {
                            searchInput.value = product.name;
                            resultsDiv.style.display = 'none';
                            addTestResult(`تم اختيار المنتج من البحث البديل: ${product.name}`, 'success');
                        });
                        
                        resultsDiv.appendChild(item);
                    });
                    resultsDiv.style.display = 'block';
                } else {
                    resultsDiv.innerHTML = '<div style="padding: 8px; color: #666;">لا توجد نتائج</div>';
                    resultsDiv.style.display = 'block';
                }
            });
            
            // إخفاء النتائج عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !resultsDiv.contains(e.target)) {
                    resultsDiv.style.display = 'none';
                }
            });
            
            addTestResult('تم إعداد البحث البديل بنجاح', 'success');
        }
        
        // اختبار البحث البديل
        function testFallbackSearch() {
            const searchValue = document.getElementById('fallbackTest').value;
            if (searchValue.trim()) {
                const foundProduct = products.find(product => 
                    product.name.toLowerCase().includes(searchValue.toLowerCase()) ||
                    (product.code && product.code.toLowerCase().includes(searchValue.toLowerCase()))
                );
                
                if (foundProduct) {
                    addTestResult(`البحث البديل يعمل! تم العثور على: ${foundProduct.name}`, 'success');
                } else {
                    addTestResult('البحث البديل لا يجد نتائج لهذا المصطلح', 'error');
                }
            } else {
                addTestResult('يرجى كتابة شيء في حقل البحث', 'error');
            }
        }
    </script>

</body>
</html>
