<?php
/**
 * اختبار رابط إعادة ضبط المصنع
 * Test Factory Reset Link
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رابط إعادة ضبط المصنع</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-bug me-3"></i>
                اختبار رابط إعادة ضبط المصنع
            </h1>
            <p class="lead">فحص وإصلاح مشكلة الرابط</p>
        </div>

        <div class="card">
            <div class="card-header bg-danger text-white">
                <h3><i class="fas fa-exclamation-triangle me-2"></i>تشخيص المشكلة</h3>
            </div>
            <div class="card-body">
                <h5>المشكلة المبلغ عنها:</h5>
                <p>عند الضغط على زر "إعادة ضبط المصنع" من صفحة الإعدادات، يتم التوجه للصفحة الرئيسية بدلاً من صفحة إعادة ضبط المصنع.</p>
                
                <h5>الأسباب المحتملة:</h5>
                <ul>
                    <li>Cache المتصفح</li>
                    <li>مشكلة في الرابط</li>
                    <li>تداخل في JavaScript</li>
                    <li>مشكلة في المسار النسبي</li>
                </ul>
                
                <h5>الحلول المطبقة:</h5>
                <ul class="text-success">
                    <li>✅ إضافة تأكيد JavaScript للرابط</li>
                    <li>✅ تحديث المسارات النسبية</li>
                    <li>✅ إضافة معرفات فريدة للروابط</li>
                </ul>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-success text-white">
                <h3><i class="fas fa-link me-2"></i>اختبار الروابط</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>الروابط المباشرة:</h5>
                        <div class="d-grid gap-2">
                            <a href="pages/settings/index.php" class="btn btn-primary">
                                <i class="fas fa-cogs me-2"></i>صفحة الإعدادات
                            </a>
                            <a href="pages/settings/factory-reset.php" class="btn btn-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>إعادة ضبط المصنع (مباشر)
                            </a>
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-home me-2"></i>الصفحة الرئيسية
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>اختبار JavaScript:</h5>
                        <div class="d-grid gap-2">
                            <button class="btn btn-warning" onclick="testFactoryResetLink()">
                                <i class="fas fa-test me-2"></i>اختبار الرابط بـ JavaScript
                            </button>
                            <button class="btn btn-info" onclick="clearCache()">
                                <i class="fas fa-refresh me-2"></i>مسح Cache المتصفح
                            </button>
                            <button class="btn btn-success" onclick="window.location.href='pages/settings/factory-reset.php'">
                                <i class="fas fa-external-link-alt me-2"></i>انتقال مباشر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-tools me-2"></i>خطوات الإصلاح</h3>
            </div>
            <div class="card-body">
                <h5>للمستخدم:</h5>
                <ol>
                    <li><strong>مسح Cache المتصفح:</strong> اضغط Ctrl+F5 أو Ctrl+Shift+R</li>
                    <li><strong>اختبار الرابط المباشر:</strong> استخدم الرابط أعلاه</li>
                    <li><strong>تجربة متصفح آخر:</strong> للتأكد من أن المشكلة ليست في المتصفح</li>
                    <li><strong>التحقق من JavaScript:</strong> تأكد من تفعيل JavaScript</li>
                </ol>
                
                <h5>للمطور:</h5>
                <ol>
                    <li>✅ تم إضافة تأكيد JavaScript للرابط</li>
                    <li>✅ تم تحديث المسارات النسبية</li>
                    <li>✅ تم إنشاء صفحة اختبار</li>
                    <li>✅ تم إضافة معرفات فريدة</li>
                </ol>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-clipboard-check me-2"></i>تعليمات الاختبار</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>خطوات الاختبار:</h6>
                    <ol>
                        <li>اضغط على "صفحة الإعدادات" أعلاه</li>
                        <li>في صفحة الإعدادات، اضغط على زر "إعادة ضبط المصنع"</li>
                        <li>يجب أن يظهر تأكيد JavaScript</li>
                        <li>اضغط "موافق" للانتقال لصفحة إعادة ضبط المصنع</li>
                        <li>إذا لم يعمل، استخدم الرابط المباشر أعلاه</li>
                    </ol>
                </div>
                
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>إذا عمل الرابط:</h6>
                    <p class="mb-0">المشكلة كانت في cache المتصفح وتم حلها.</p>
                </div>
                
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>إذا لم يعمل الرابط:</h6>
                    <p class="mb-0">يرجى الإبلاغ عن تفاصيل إضافية (نوع المتصفح، رسائل الخطأ، إلخ).</p>
                </div>
            </div>
        </div>

        <div class="text-center text-white mt-4">
            <h3>🔧 تم تطبيق الإصلاحات 🔧</h3>
            <p>جرب الروابط أعلاه للتأكد من عمل إعادة ضبط المصنع</p>
            
            <div class="alert alert-warning mt-3">
                <strong>ملاحظة:</strong> احذف هذا الملف بعد التأكد من حل المشكلة.
            </div>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testFactoryResetLink() {
            if (confirm('هل تريد اختبار الانتقال لصفحة إعادة ضبط المصنع؟')) {
                window.location.href = 'pages/settings/factory-reset.php';
            }
        }
        
        function clearCache() {
            if (confirm('هل تريد إعادة تحميل الصفحة لمسح Cache؟')) {
                window.location.reload(true);
            }
        }
        
        // عرض معلومات المتصفح
        document.addEventListener('DOMContentLoaded', function() {
            console.log('معلومات المتصفح:');
            console.log('User Agent:', navigator.userAgent);
            console.log('URL الحالي:', window.location.href);
            console.log('المسار:', window.location.pathname);
        });
    </script>

</body>
</html>
