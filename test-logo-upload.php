<?php
/**
 * اختبار رفع الشعار - صفحة تشخيص المشاكل
 * Logo Upload Test - Troubleshooting Page
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رفع الشعار - نظام Zero</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
            border: none;
        }
        .test-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
        .test-success {
            border-left-color: #28a745;
        }
        .test-warning {
            border-left-color: #ffc107;
        }
        .test-error {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- العنوان الرئيسي -->
        <div class="text-center text-white mb-4">
            <h1 class="display-4">
                <i class="fas fa-bug me-3"></i>
                اختبار رفع الشعار
            </h1>
            <p class="lead">تشخيص مشاكل رفع الملفات</p>
        </div>

        <!-- اختبار إعدادات الخادم -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-server me-2"></i>اختبار إعدادات الخادم</h3>
            </div>
            <div class="card-body">
                
                <?php
                // اختبار رفع الملفات
                $upload_enabled = ini_get('file_uploads');
                $max_file_size = ini_get('upload_max_filesize');
                $max_post_size = ini_get('post_max_size');
                $memory_limit = ini_get('memory_limit');
                ?>
                
                <div class="test-item <?php echo $upload_enabled ? 'test-success' : 'test-error'; ?>">
                    <h6><i class="fas fa-<?php echo $upload_enabled ? 'check' : 'times'; ?> me-2"></i>رفع الملفات</h6>
                    <p class="mb-0">الحالة: <?php echo $upload_enabled ? 'مفعل' : 'معطل'; ?></p>
                </div>
                
                <div class="test-item test-success">
                    <h6><i class="fas fa-info me-2"></i>حد حجم الملف</h6>
                    <p class="mb-0">الحد الأقصى: <?php echo $max_file_size; ?></p>
                </div>
                
                <div class="test-item test-success">
                    <h6><i class="fas fa-info me-2"></i>حد حجم POST</h6>
                    <p class="mb-0">الحد الأقصى: <?php echo $max_post_size; ?></p>
                </div>
                
                <div class="test-item test-success">
                    <h6><i class="fas fa-memory me-2"></i>حد الذاكرة</h6>
                    <p class="mb-0">الحد الأقصى: <?php echo $memory_limit; ?></p>
                </div>
                
                <?php
                // اختبار الدوال المطلوبة
                $functions_to_test = [
                    'mime_content_type' => 'تحديد نوع MIME',
                    'finfo_open' => 'معلومات الملف (finfo)',
                    'getimagesize' => 'معلومات الصورة',
                    'move_uploaded_file' => 'نقل الملف المرفوع'
                ];
                
                foreach ($functions_to_test as $function => $description) {
                    $exists = function_exists($function);
                    echo '<div class="test-item ' . ($exists ? 'test-success' : 'test-warning') . '">';
                    echo '<h6><i class="fas fa-' . ($exists ? 'check' : 'exclamation-triangle') . ' me-2"></i>' . $description . '</h6>';
                    echo '<p class="mb-0">الدالة ' . $function . ': ' . ($exists ? 'متاحة' : 'غير متاحة') . '</p>';
                    echo '</div>';
                }
                ?>
                
                <?php
                // اختبار مجلد الرفع
                $upload_dir = 'uploads/logo/';
                $dir_exists = is_dir($upload_dir);
                $dir_writable = is_writable($upload_dir);
                ?>
                
                <div class="test-item <?php echo $dir_exists ? 'test-success' : 'test-error'; ?>">
                    <h6><i class="fas fa-<?php echo $dir_exists ? 'check' : 'times'; ?> me-2"></i>مجلد الرفع</h6>
                    <p class="mb-0">المجلد <?php echo $upload_dir; ?>: <?php echo $dir_exists ? 'موجود' : 'غير موجود'; ?></p>
                </div>
                
                <?php if ($dir_exists): ?>
                <div class="test-item <?php echo $dir_writable ? 'test-success' : 'test-error'; ?>">
                    <h6><i class="fas fa-<?php echo $dir_writable ? 'check' : 'times'; ?> me-2"></i>صلاحية الكتابة</h6>
                    <p class="mb-0">صلاحية الكتابة في المجلد: <?php echo $dir_writable ? 'متاحة' : 'غير متاحة'; ?></p>
                </div>
                <?php endif; ?>
                
            </div>
        </div>

        <!-- اختبار بسيط لرفع الملف -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h3><i class="fas fa-upload me-2"></i>اختبار رفع ملف</h3>
            </div>
            <div class="card-body">
                
                <?php
                if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])) {
                    echo '<div class="alert alert-info">';
                    echo '<h6>معلومات الملف المرفوع:</h6>';
                    
                    $file = $_FILES['test_file'];
                    echo '<ul>';
                    echo '<li><strong>اسم الملف:</strong> ' . htmlspecialchars($file['name']) . '</li>';
                    echo '<li><strong>حجم الملف:</strong> ' . number_format($file['size'] / 1024, 2) . ' KB</li>';
                    echo '<li><strong>نوع الملف (من المتصفح):</strong> ' . htmlspecialchars($file['type']) . '</li>';
                    echo '<li><strong>رمز الخطأ:</strong> ' . $file['error'] . '</li>';
                    
                    if ($file['error'] === UPLOAD_ERR_OK) {
                        $temp_file = $file['tmp_name'];
                        
                        // اختبار mime_content_type
                        if (function_exists('mime_content_type')) {
                            $mime_type = @mime_content_type($temp_file);
                            echo '<li><strong>نوع MIME (mime_content_type):</strong> ' . htmlspecialchars($mime_type ?: 'فشل في التحديد') . '</li>';
                        }
                        
                        // اختبار finfo
                        if (function_exists('finfo_open')) {
                            $finfo = @finfo_open(FILEINFO_MIME_TYPE);
                            if ($finfo) {
                                $finfo_type = @finfo_file($finfo, $temp_file);
                                echo '<li><strong>نوع MIME (finfo):</strong> ' . htmlspecialchars($finfo_type ?: 'فشل في التحديد') . '</li>';
                                @finfo_close($finfo);
                            }
                        }
                        
                        // اختبار getimagesize
                        $image_info = @getimagesize($temp_file);
                        if ($image_info) {
                            echo '<li><strong>معلومات الصورة:</strong> ' . $image_info[0] . 'x' . $image_info[1] . ' بكسل</li>';
                            echo '<li><strong>نوع الصورة:</strong> ' . $image_info['mime'] . '</li>';
                        } else {
                            echo '<li><strong>معلومات الصورة:</strong> فشل في قراءة الصورة</li>';
                        }
                        
                        // اختبار امتداد الملف
                        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                        echo '<li><strong>امتداد الملف:</strong> ' . htmlspecialchars($extension) . '</li>';
                        
                        // التحقق من الأنواع المسموحة
                        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                        $allowed_mime_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                        
                        $extension_ok = in_array($extension, $allowed_extensions);
                        $mime_ok = isset($mime_type) ? in_array($mime_type, $allowed_mime_types) : true;
                        $finfo_ok = isset($finfo_type) ? in_array($finfo_type, $allowed_mime_types) : true;
                        $image_ok = $image_info !== false;
                        
                        echo '<li><strong>امتداد مقبول:</strong> ' . ($extension_ok ? 'نعم' : 'لا') . '</li>';
                        echo '<li><strong>نوع MIME مقبول:</strong> ' . ($mime_ok ? 'نعم' : 'لا') . '</li>';
                        echo '<li><strong>نوع finfo مقبول:</strong> ' . ($finfo_ok ? 'نعم' : 'لا') . '</li>';
                        echo '<li><strong>صورة صحيحة:</strong> ' . ($image_ok ? 'نعم' : 'لا') . '</li>';
                        
                        if ($extension_ok && $mime_ok && $finfo_ok && $image_ok) {
                            echo '</ul>';
                            echo '<div class="alert alert-success mt-3">✅ الملف يجب أن يعمل بشكل صحيح!</div>';
                        } else {
                            echo '</ul>';
                            echo '<div class="alert alert-danger mt-3">❌ الملف قد لا يعمل. راجع المعلومات أعلاه.</div>';
                        }
                        
                    } else {
                        echo '</ul>';
                        $error_messages = [
                            UPLOAD_ERR_INI_SIZE => 'الملف أكبر من الحد المسموح في إعدادات PHP',
                            UPLOAD_ERR_FORM_SIZE => 'الملف أكبر من الحد المسموح في النموذج',
                            UPLOAD_ERR_PARTIAL => 'تم رفع جزء من الملف فقط',
                            UPLOAD_ERR_NO_FILE => 'لم يتم رفع أي ملف',
                            UPLOAD_ERR_NO_TMP_DIR => 'مجلد الملفات المؤقتة غير موجود',
                            UPLOAD_ERR_CANT_WRITE => 'فشل في كتابة الملف',
                            UPLOAD_ERR_EXTENSION => 'امتداد PHP أوقف رفع الملف'
                        ];
                        
                        $error_msg = isset($error_messages[$file['error']]) ? $error_messages[$file['error']] : 'خطأ غير معروف';
                        echo '<div class="alert alert-danger mt-3">❌ خطأ في رفع الملف: ' . $error_msg . '</div>';
                    }
                    
                    echo '</div>';
                }
                ?>
                
                <form method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="test_file" class="form-label">اختر ملف صورة للاختبار:</label>
                        <input type="file" class="form-control" id="test_file" name="test_file" accept="image/*" required>
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload me-2"></i>
                        اختبار رفع الملف
                    </button>
                </form>
            </div>
        </div>

        <!-- الحلول المقترحة -->
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-tools me-2"></i>الحلول المقترحة</h3>
            </div>
            <div class="card-body">
                <h6>إذا كان لديك مشاكل في رفع الشعار، جرب الحلول التالية:</h6>
                
                <div class="test-item">
                    <h6><i class="fas fa-1 me-2"></i>تأكد من نوع الملف</h6>
                    <p class="mb-0">استخدم ملفات JPG أو PNG أو GIF فقط. تجنب الملفات المحولة أو المضغوطة.</p>
                </div>
                
                <div class="test-item">
                    <h6><i class="fas fa-2 me-2"></i>تحقق من حجم الملف</h6>
                    <p class="mb-0">تأكد أن حجم الملف أقل من 5 ميجابايت. قم بضغط الصورة إذا لزم الأمر.</p>
                </div>
                
                <div class="test-item">
                    <h6><i class="fas fa-3 me-2"></i>استخدم صورة بسيطة</h6>
                    <p class="mb-0">جرب صورة بسيطة أولاً (مثل لقطة شاشة محفوظة كـ PNG) للتأكد من عمل النظام.</p>
                </div>
                
                <div class="test-item">
                    <h6><i class="fas fa-4 me-2"></i>تحقق من صلاحيات المجلد</h6>
                    <p class="mb-0">تأكد أن مجلد uploads/logo/ موجود وله صلاحية الكتابة (755 أو 777).</p>
                </div>
                
                <div class="test-item">
                    <h6><i class="fas fa-5 me-2"></i>جرب متصفح آخر</h6>
                    <p class="mb-0">أحياناً المتصفح قد يسبب مشاكل. جرب Chrome أو Firefox.</p>
                </div>
            </div>
        </div>

        <!-- روابط مفيدة -->
        <div class="text-center text-white mt-4">
            <a href="pages/settings/logo.php" class="btn btn-light btn-lg me-3">
                <i class="fas fa-image me-2"></i>
                العودة لإدارة الشعار
            </a>
            <a href="pages/settings/index.php" class="btn btn-outline-light btn-lg">
                <i class="fas fa-cogs me-2"></i>
                الإعدادات
            </a>
            
            <div class="alert alert-info mt-4">
                <strong>ملاحظة:</strong> احذف هذا الملف بعد حل المشكلة:
                <br><code>test-logo-upload.php</code>
            </div>
        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
